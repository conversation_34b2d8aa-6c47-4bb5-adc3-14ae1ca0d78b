"""
姓名生成器模块
从数据集中随机生成姓名
"""
import random
import os
from typing import Tuple, List, Optional
from logger.logger import get_logger


class NameGenerator:
    """姓名生成器"""
    
    def __init__(self, dataset_path: str = None):
        """
        初始化姓名生成器
        
        Args:
            dataset_path (str): 姓名数据集文件路径
        """
        self.logger = get_logger("NameGenerator")
        self.names = []
        self.dataset_path = dataset_path or self._find_dataset_path()
        self._load_names()

    def _find_dataset_path(self) -> str:
        """
        查找姓名数据集文件路径
        
        Returns:
            str: 数据集文件路径
        """
        # 可能的路径列表
        possible_paths = [
            'names-dataset.txt',
            '../names-dataset.txt',
            '../../names-dataset.txt',
            os.path.join(os.path.dirname(__file__), '..', '..', 'names-dataset.txt'),
            os.path.join(os.getcwd(), 'names-dataset.txt')
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                self.logger.info(f"找到姓名数据集: {path}")
                return path
        
        # 如果找不到数据集文件，使用内置的默认姓名
        self.logger.warning("未找到姓名数据集文件，将使用内置默认姓名")
        return None

    def _load_names(self):
        """加载姓名数据"""
        if self.dataset_path and os.path.exists(self.dataset_path):
            try:
                with open(self.dataset_path, 'r', encoding='utf-8') as f:
                    self.names = [line.strip() for line in f if line.strip()]
                
                self.logger.info(f"成功加载 {len(self.names)} 个姓名")
                
            except Exception as e:
                self.logger.error(f"加载姓名数据集失败: {e}")
                self._load_default_names()
        else:
            self._load_default_names()

    def _load_default_names(self):
        """加载默认姓名列表"""
        self.names = [
            "James Smith", "Mary Johnson", "John Williams", "Patricia Brown",
            "Robert Jones", "Jennifer Garcia", "Michael Miller", "Linda Davis",
            "William Rodriguez", "Elizabeth Martinez", "David Wilson", "Barbara Anderson",
            "Richard Taylor", "Susan Thomas", "Joseph Jackson", "Jessica White",
            "Thomas Harris", "Sarah Martin", "Christopher Garcia", "Karen Thompson",
            "Charles Martinez", "Nancy Robinson", "Daniel Clark", "Lisa Lewis",
            "Matthew Rodriguez", "Betty Lee", "Anthony Walker", "Helen Hall",
            "Mark Allen", "Sandra Young", "Donald Hernandez", "Donna King",
            "Steven Wright", "Carol Lopez", "Paul Hill", "Ruth Scott",
            "Andrew Green", "Sharon Adams", "Joshua Baker", "Michelle Gonzalez",
            "Kenneth Nelson", "Laura Carter", "Kevin Mitchell", "Emily Perez",
            "Brian Roberts", "Kimberly Turner", "George Phillips", "Deborah Campbell",
            "Edward Parker", "Dorothy Evans", "Ronald Edwards", "Amy Collins",
            "Timothy Stewart", "Angela Sanchez", "Jason Flores", "Brenda Morris",
            "Jeffrey Butler", "Emma Rogers", "Ryan Reed", "Olivia Cook",
            "Jacob Morgan", "Ava Bailey", "Gary Rivera", "Sophia Cooper",
            "Nicholas Richardson", "Isabella Cox", "Eric Ward", "Mia Howard",
            "Jonathan Torres", "Charlotte Ward", "Stephen Peterson", "Amelia Gray",
            "Larry Ramirez", "Harper James", "Justin Watson", "Evelyn Brooks",
            "Scott Kelly", "Abigail Sanders", "Brandon Price", "Emily Bennett",
            "Benjamin Wood", "Elizabeth Wood", "Samuel Barnes", "Mila Ross",
            "Gregory Henderson", "Ella Coleman", "Alexander Jenkins", "Avery Perry",
            "Patrick Powell", "Scarlett Long", "Frank Patterson", "Madison Hughes",
            "Raymond Hughes", "Luna Flores", "Jack Washington", "Grace Butler",
            "Dennis Butler", "Chloe Simmons", "Jerry Foster", "Victoria Gonzales",
            "Tyler Gonzales", "Aria Bryant", "Aaron Alexander", "Zoe Russell",
            "Jose Griffin", "Nora Griffin", "Henry Diaz", "Lily Hayes",
            "Adam Myers", "Ellie Ford", "Douglas Ford", "Nova Hamilton",
            "Nathan Graham", "Hazel Sullivan", "Peter Wallace", "Violet Woods",
            "Zachary Woods", "Aurora Watson", "Kyle Palmer", "Savannah Barnes",
            "Walter Barnes", "Audrey Ross", "Harold Henderson", "Brooklyn Henderson",
            "Jeremy Coleman", "Bella Coleman", "Ethan Jenkins", "Claire Jenkins",
            "Arthur Perry", "Skylar Perry", "Sean Long", "Lucy Long",
            "Carl Hughes", "Paisley Hughes", "Harold Flores", "Everly Flores",
            "Jordan Butler", "Anna Butler", "Ralph Simmons", "Caroline Simmons",
            "Wayne Bryant", "Genesis Bryant", "Roy Russell", "Aaliyah Russell",
            "Eugene Hayes", "Kennedy Hayes", "Louis Ford", "Kinsley Ford",
            "Philip Hamilton", "Allison Hamilton", "Bobby Sullivan", "Maya Sullivan"
        ]
        
        self.logger.info(f"使用内置默认姓名列表: {len(self.names)} 个姓名")

    def generate_name(self) -> Tuple[str, str]:
        """
        生成随机姓名
        
        Returns:
            tuple: (first_name, last_name)
        """
        if not self.names:
            raise ValueError("姓名数据为空")
        
        full_name = random.choice(self.names)
        
        # 分割姓名
        name_parts = full_name.split()
        if len(name_parts) >= 2:
            first_name = name_parts[0]
            last_name = ' '.join(name_parts[1:])  # 处理多个姓氏的情况
        else:
            # 如果只有一个词，作为名字，随机生成姓氏
            first_name = name_parts[0]
            last_name = self._generate_random_lastname()
        
        self.logger.debug(f"生成姓名: {first_name} {last_name}")
        return first_name, last_name

    def _generate_random_lastname(self) -> str:
        """
        生成随机姓氏
        
        Returns:
            str: 随机姓氏
        """
        common_lastnames = [
            "Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller",
            "Davis", "Rodriguez", "Martinez", "Hernandez", "Lopez", "Gonzalez",
            "Wilson", "Anderson", "Thomas", "Taylor", "Moore", "Jackson", "Martin",
            "Lee", "Perez", "Thompson", "White", "Harris", "Sanchez", "Clark",
            "Ramirez", "Lewis", "Robinson", "Walker", "Young", "Allen", "King",
            "Wright", "Scott", "Torres", "Nguyen", "Hill", "Flores", "Green",
            "Adams", "Nelson", "Baker", "Hall", "Rivera", "Campbell", "Mitchell",
            "Carter", "Roberts", "Gomez", "Phillips", "Evans", "Turner", "Diaz"
        ]
        
        return random.choice(common_lastnames)

    def generate_username(self, max_length: int = 15) -> str:
        """
        生成用户名（基于姓名）
        
        Args:
            max_length (int): 最大长度
            
        Returns:
            str: 生成的用户名
        """
        first_name, last_name = self.generate_name()
        
        # 生成用户名的几种方式
        username_patterns = [
            first_name.lower(),
            f"{first_name.lower()}{last_name.lower()}",
            f"{first_name.lower()}.{last_name.lower()}",
            f"{first_name.lower()}_{last_name.lower()}",
            f"{first_name[0].lower()}{last_name.lower()}",
            f"{first_name.lower()}{last_name[0].lower()}"
        ]
        
        # 选择一个合适长度的用户名
        for pattern in username_patterns:
            if len(pattern) <= max_length:
                return pattern
        
        # 如果都太长，截断第一个模式
        return username_patterns[0][:max_length]

    def generate_display_name(self) -> str:
        """
        生成显示名称
        
        Returns:
            str: 显示名称
        """
        first_name, last_name = self.generate_name()
        
        # 显示名称的几种格式
        display_formats = [
            f"{first_name} {last_name}",
            f"{first_name} {last_name[0]}.",
            first_name,
            f"{first_name} {last_name}"
        ]
        
        return random.choice(display_formats)

    def generate_first_name(self) -> str:
        """
        只生成名字
        
        Returns:
            str: 名字
        """
        first_name, _ = self.generate_name()
        return first_name

    def generate_last_name(self) -> str:
        """
        只生成姓氏
        
        Returns:
            str: 姓氏
        """
        _, last_name = self.generate_name()
        return last_name

    def generate_multiple_names(self, count: int) -> List[Tuple[str, str]]:
        """
        生成多个姓名
        
        Args:
            count (int): 生成数量
            
        Returns:
            list: 姓名列表
        """
        names = []
        for _ in range(count):
            names.append(self.generate_name())
        
        return names

    def get_random_name_from_list(self, name_list: List[str]) -> Tuple[str, str]:
        """
        从指定列表中获取随机姓名
        
        Args:
            name_list (list): 姓名列表
            
        Returns:
            tuple: (first_name, last_name)
        """
        if not name_list:
            return self.generate_name()
        
        full_name = random.choice(name_list)
        name_parts = full_name.split()
        
        if len(name_parts) >= 2:
            return name_parts[0], ' '.join(name_parts[1:])
        else:
            return name_parts[0], self._generate_random_lastname()

    def is_valid_name(self, name: str) -> bool:
        """
        验证姓名是否有效
        
        Args:
            name (str): 姓名
            
        Returns:
            bool: 是否有效
        """
        if not name or not isinstance(name, str):
            return False
        
        # 基本验证：只包含字母、空格、点号、连字符
        import re
        pattern = r'^[a-zA-Z\s\.\-\']+$'
        
        if not re.match(pattern, name):
            return False
        
        # 长度验证
        if len(name) < 2 or len(name) > 50:
            return False
        
        return True

    def get_dataset_info(self) -> dict:
        """
        获取数据集信息
        
        Returns:
            dict: 数据集信息
        """
        return {
            'dataset_path': self.dataset_path,
            'total_names': len(self.names),
            'using_default': self.dataset_path is None
        }
