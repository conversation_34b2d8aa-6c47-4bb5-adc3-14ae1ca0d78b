"""
账号信息生成模块
生成随机的账号信息，包括邮箱、密码、姓名等
"""
import random
import string
import time
from typing import Dict, Optional
from logger.logger import get_logger
from .names import NameGenerator


class AccountGenerator:
    """账号信息生成器"""
    
    def __init__(self, domain: str = None, config=None):
        """
        初始化账号生成器
        
        Args:
            domain (str): 邮箱域名
            config: 配置对象
        """
        self.domain = domain
        self.config = config
        self.name_generator = NameGenerator()
        self.logger = get_logger("AccountGenerator")

    def generate_account_info(self, email_length: int = 4) -> Dict[str, str]:
        """
        生成完整的账号信息
        
        Args:
            email_length (int): 邮箱用户名随机部分长度
            
        Returns:
            dict: 包含email, password, first_name, last_name的账号信息
        """
        try:
            # 生成姓名
            first_name, last_name = self.name_generator.generate_name()
            
            # 生成邮箱
            email = self.generate_email(first_name, email_length)
            
            # 生成密码
            password = self.generate_password()
            
            account_info = {
                'email': email,
                'password': password,
                'first_name': first_name,
                'last_name': last_name
            }
            
            self.logger.info(f"生成账号信息: {email}")
            return account_info
            
        except Exception as e:
            self.logger.error(f"生成账号信息失败: {e}")
            raise

    def generate_email(self, base_name: str = None, random_length: int = 4) -> str:
        """
        生成邮箱地址
        
        Args:
            base_name (str): 基础名称，如果为None则随机生成
            random_length (int): 随机部分长度
            
        Returns:
            str: 邮箱地址
        """
        if not self.domain:
            raise ValueError("邮箱域名未配置")
        
        if not base_name:
            base_name = self.name_generator.generate_username()
        
        # 生成随机后缀
        random_suffix = self._generate_random_suffix(random_length)
        
        # 组合邮箱地址
        email = f"{base_name}{random_suffix}@{self.domain}"
        
        self.logger.debug(f"生成邮箱: {email}")
        return email

    def _generate_random_suffix(self, length: int) -> str:
        """
        生成随机后缀
        
        Args:
            length (int): 后缀长度
            
        Returns:
            str: 随机后缀
        """
        if length <= 0:
            return ""
        
        # 使用时间戳的后几位作为随机数
        timestamp = str(int(time.time()))
        if len(timestamp) >= length:
            return timestamp[-length:]
        else:
            # 如果时间戳不够长，补充随机数字
            remaining = length - len(timestamp)
            random_digits = ''.join(random.choices(string.digits, k=remaining))
            return timestamp + random_digits

    def generate_password(self, length: int = 12, 
                         include_uppercase: bool = True,
                         include_lowercase: bool = True,
                         include_digits: bool = True,
                         include_symbols: bool = True) -> str:
        """
        生成随机密码
        
        Args:
            length (int): 密码长度
            include_uppercase (bool): 是否包含大写字母
            include_lowercase (bool): 是否包含小写字母
            include_digits (bool): 是否包含数字
            include_symbols (bool): 是否包含特殊符号
            
        Returns:
            str: 生成的密码
        """
        characters = ""
        
        if include_lowercase:
            characters += string.ascii_lowercase
        if include_uppercase:
            characters += string.ascii_uppercase
        if include_digits:
            characters += string.digits
        if include_symbols:
            characters += "!@#$%^&*"
        
        if not characters:
            raise ValueError("至少需要包含一种字符类型")
        
        password = ''.join(random.choices(characters, k=length))
        
        self.logger.debug("生成密码完成")
        return password

    def generate_simple_password(self, length: int = 8) -> str:
        """
        生成简单密码（只包含字母和数字）
        
        Args:
            length (int): 密码长度
            
        Returns:
            str: 生成的密码
        """
        return self.generate_password(
            length=length,
            include_uppercase=True,
            include_lowercase=True,
            include_digits=True,
            include_symbols=False
        )

    def generate_strong_password(self, length: int = 16) -> str:
        """
        生成强密码
        
        Args:
            length (int): 密码长度
            
        Returns:
            str: 生成的强密码
        """
        return self.generate_password(
            length=length,
            include_uppercase=True,
            include_lowercase=True,
            include_digits=True,
            include_symbols=True
        )

    def generate_username(self, base_name: str = None, max_length: int = 20) -> str:
        """
        生成用户名
        
        Args:
            base_name (str): 基础名称
            max_length (int): 最大长度
            
        Returns:
            str: 生成的用户名
        """
        if not base_name:
            base_name = self.name_generator.generate_username()
        
        # 确保用户名不超过最大长度
        if len(base_name) > max_length:
            base_name = base_name[:max_length]
        
        # 添加随机数字后缀
        random_suffix = random.randint(10, 999)
        username = f"{base_name}{random_suffix}"
        
        # 再次检查长度
        if len(username) > max_length:
            # 如果还是太长，截断基础名称
            available_length = max_length - len(str(random_suffix))
            if available_length > 0:
                username = f"{base_name[:available_length]}{random_suffix}"
            else:
                username = str(random_suffix)
        
        self.logger.debug(f"生成用户名: {username}")
        return username

    def generate_phone_number(self, country_code: str = "+1") -> str:
        """
        生成随机手机号码
        
        Args:
            country_code (str): 国家代码
            
        Returns:
            str: 生成的手机号码
        """
        # 生成10位数字（美国格式）
        area_code = random.randint(200, 999)  # 避免以0或1开头
        exchange = random.randint(200, 999)
        number = random.randint(1000, 9999)
        
        phone = f"{country_code}{area_code}{exchange}{number}"
        
        self.logger.debug(f"生成手机号: {phone}")
        return phone

    def generate_birth_date(self, min_age: int = 18, max_age: int = 65) -> Dict[str, int]:
        """
        生成随机生日
        
        Args:
            min_age (int): 最小年龄
            max_age (int): 最大年龄
            
        Returns:
            dict: 包含year, month, day的生日信息
        """
        import datetime
        
        current_year = datetime.datetime.now().year
        birth_year = random.randint(current_year - max_age, current_year - min_age)
        birth_month = random.randint(1, 12)
        
        # 根据月份确定天数
        days_in_month = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]
        
        # 检查闰年
        if birth_month == 2 and self._is_leap_year(birth_year):
            max_day = 29
        else:
            max_day = days_in_month[birth_month - 1]
        
        birth_day = random.randint(1, max_day)
        
        birth_date = {
            'year': birth_year,
            'month': birth_month,
            'day': birth_day
        }
        
        self.logger.debug(f"生成生日: {birth_year}-{birth_month:02d}-{birth_day:02d}")
        return birth_date

    def _is_leap_year(self, year: int) -> bool:
        """
        判断是否为闰年
        
        Args:
            year (int): 年份
            
        Returns:
            bool: 是否为闰年
        """
        return year % 4 == 0 and (year % 100 != 0 or year % 400 == 0)

    def generate_batch_accounts(self, count: int, email_length: int = 4) -> list:
        """
        批量生成账号信息
        
        Args:
            count (int): 生成数量
            email_length (int): 邮箱随机部分长度
            
        Returns:
            list: 账号信息列表
        """
        accounts = []
        
        for i in range(count):
            try:
                account = self.generate_account_info(email_length)
                accounts.append(account)
                self.logger.info(f"生成第 {i+1}/{count} 个账号")
            except Exception as e:
                self.logger.error(f"生成第 {i+1} 个账号失败: {e}")
                continue
        
        self.logger.info(f"批量生成完成: {len(accounts)}/{count}")
        return accounts

    def validate_account_info(self, account_info: Dict[str, str]) -> bool:
        """
        验证账号信息的有效性
        
        Args:
            account_info (dict): 账号信息
            
        Returns:
            bool: 账号信息是否有效
        """
        required_fields = ['email', 'password', 'first_name', 'last_name']
        
        for field in required_fields:
            if field not in account_info or not account_info[field]:
                self.logger.error(f"账号信息缺少必要字段: {field}")
                return False
        
        # 验证邮箱格式
        email = account_info['email']
        if '@' not in email or '.' not in email.split('@')[1]:
            self.logger.error(f"邮箱格式无效: {email}")
            return False
        
        # 验证密码长度
        password = account_info['password']
        if len(password) < 6:
            self.logger.error("密码长度不足6位")
            return False
        
        return True
