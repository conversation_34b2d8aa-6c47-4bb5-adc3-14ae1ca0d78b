"""
配置管理模块
统一管理应用程序的配置信息
"""
import os
import sys
import json
from typing import Dict, Any, Optional
from dotenv import load_dotenv
from logger.logger import get_logger


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = None, env_file: str = None):
        """
        初始化配置管理器
        
        Args:
            config_file (str): 配置文件路径
            env_file (str): 环境变量文件路径
        """
        self.logger = get_logger("ConfigManager")

        # 确定应用程序根目录
        self.app_root = self._get_app_root()
        
        # 配置文件路径
        self.config_file = config_file or os.path.join(self.app_root, "config.json")
        self.env_file = env_file or os.path.join(self.app_root, ".env")
        
        # 配置数据
        self.config_data = {}
        self.env_data = {}
        
        # 加载配置
        self._load_env_file()
        self._load_config_file()

    def _get_app_root(self) -> str:
        """
        获取应用程序根目录
        
        Returns:
            str: 应用程序根目录路径
        """
        if getattr(sys, "frozen", False):
            # 打包后的可执行文件
            return os.path.dirname(sys.executable)
        else:
            # 开发环境
            return os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    def _load_env_file(self):
        """加载环境变量文件"""
        if os.path.exists(self.env_file):
            try:
                load_dotenv(self.env_file)
                self.logger.info(f"成功加载环境变量文件: {self.env_file}")
                
                # 读取所有环境变量到字典中
                with open(self.env_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            key, value = line.split('=', 1)
                            self.env_data[key.strip()] = value.strip().strip('"\'')
                            
            except Exception as e:
                self.logger.error(f"加载环境变量文件失败: {e}")
                raise FileNotFoundError(f"环境变量文件不存在或无法读取: {self.env_file}")
        else:
            self.logger.warning(f"环境变量文件不存在: {self.env_file}")

    def _load_config_file(self):
        """加载JSON配置文件"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config_data = json.load(f)
                self.logger.info(f"成功加载配置文件: {self.config_file}")
            except Exception as e:
                self.logger.error(f"加载配置文件失败: {e}")
                self.config_data = {}
        else:
            self.logger.info(f"配置文件不存在，使用默认配置: {self.config_file}")

    def get(self, key: str, default: Any = None, from_env: bool = True) -> Any:
        """
        获取配置值
        
        Args:
            key (str): 配置键
            default (Any): 默认值
            from_env (bool): 是否优先从环境变量获取
            
        Returns:
            Any: 配置值
        """
        # 优先从环境变量获取
        if from_env:
            env_value = os.getenv(key)
            if env_value is not None:
                return self._convert_value(env_value)
        
        # 从配置文件获取
        if key in self.config_data:
            return self.config_data[key]
        
        # 返回默认值
        return default

    def _convert_value(self, value) -> Any:
        """
        转换值为适当的类型

        Args:
            value: 要转换的值

        Returns:
            Any: 转换后的值
        """
        # 如果已经是非字符串类型，直接返回
        if not isinstance(value, str):
            return value

        # 转换字符串值
        value_lower = value.lower()
        if value_lower in ('true', 'false'):
            return value_lower == 'true'

        if value.isdigit():
            return int(value)

        try:
            return float(value)
        except ValueError:
            pass

        return value

    def set(self, key: str, value: Any):
        """
        设置配置值
        
        Args:
            key (str): 配置键
            value (Any): 配置值
        """
        self.config_data[key] = value

    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, indent=2, ensure_ascii=False)
            self.logger.info(f"配置已保存到: {self.config_file}")
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")

    # 邮箱配置相关方法
    def get_temp_mail_config(self) -> Dict[str, str]:
        """获取临时邮箱配置"""
        temp_mail_value = self.get('TEMP_MAIL', '')
        temp_mail_str = str(temp_mail_value) if temp_mail_value else ''
        username = temp_mail_str.strip().split('@')[0] if temp_mail_str else ''

        epin_value = self.get('TEMP_MAIL_EPIN', '')
        epin = str(epin_value).strip() if epin_value else ''

        extension_value = self.get('TEMP_MAIL_EXT', '')
        extension = str(extension_value).strip() if extension_value else ''

        domain_value = self.get('DOMAIN', '')
        domain = str(domain_value).strip() if domain_value else ''

        return {
            'username': username,
            'epin': epin,
            'extension': extension,
            'domain': domain
        }

    def get_imap_config(self) -> Optional[Dict[str, str]]:
        """获取IMAP配置"""
        temp_mail_value = self.get('TEMP_MAIL', '')
        temp_mail = str(temp_mail_value).strip() if temp_mail_value else ''

        if temp_mail == "null":
            server_value = self.get('IMAP_SERVER', '')
            server = str(server_value).strip() if server_value else ''

            port_value = self.get('IMAP_PORT', '')
            port = str(port_value).strip() if port_value else ''

            username_value = self.get('IMAP_USER', '')
            username = str(username_value).strip() if username_value else ''

            password_value = self.get('IMAP_PASS', '')
            password = str(password_value).strip() if password_value else ''

            folder_value = self.get('IMAP_DIR', 'INBOX')
            folder = str(folder_value).strip() if folder_value else 'INBOX'

            protocol_value = self.get('IMAP_PROTOCOL', 'IMAP')
            protocol = str(protocol_value).strip() if protocol_value else 'IMAP'

            return {
                'server': server,
                'port': port,
                'username': username,
                'password': password,
                'folder': folder,
                'protocol': protocol
            }

        return None

    def is_using_imap(self) -> bool:
        """是否使用IMAP"""
        temp_mail_value = self.get('TEMP_MAIL', '')
        temp_mail = str(temp_mail_value).strip() if temp_mail_value else ''
        return temp_mail == "null"

    def get_domain(self) -> str:
        """获取邮箱域名"""
        domain_value = self.get('DOMAIN', '')
        return str(domain_value).strip() if domain_value else ''

    def get_protocol(self) -> str:
        """获取邮件协议"""
        protocol_value = self.get('IMAP_PROTOCOL', 'IMAP')
        return str(protocol_value).strip() if protocol_value else 'IMAP'

    # 浏览器配置相关方法
    def get_browser_config(self) -> Dict[str, Any]:
        """获取浏览器配置"""
        headless_value = self.get('BROWSER_HEADLESS', 'True')
        if isinstance(headless_value, bool):
            headless = headless_value
        else:
            headless = str(headless_value).lower() == 'true'

        proxy_value = self.get('BROWSER_PROXY', '')
        proxy = str(proxy_value).strip() if proxy_value else ''

        browser_path_value = self.get('BROWSER_PATH', '')
        browser_path = str(browser_path_value).strip() if browser_path_value else ''

        user_agent_value = self.get('USER_AGENT', '')
        user_agent = str(user_agent_value).strip() if user_agent_value else ''

        return {
            'headless': headless,
            'proxy': proxy,
            'browser_path': browser_path,
            'user_agent': user_agent
        }

    # 应用配置相关方法
    def get_app_config(self) -> Dict[str, Any]:
        """获取应用配置"""
        flow_type_value = self.get('FLOW_TYPE', 'cursor')
        flow_type = str(flow_type_value).strip().lower() if flow_type_value else 'cursor'

        screenshot_value = self.get('SCREENSHOT_ENABLED', 'True')
        if isinstance(screenshot_value, bool):
            screenshot_enabled = screenshot_value
        else:
            screenshot_enabled = str(screenshot_value).lower() == 'true'

        log_level_value = self.get('LOG_LEVEL', 'INFO')
        log_level = str(log_level_value).strip().upper() if log_level_value else 'INFO'

        max_retries_value = self.get('MAX_RETRIES', '3')
        try:
            max_retries = int(max_retries_value)
        except (ValueError, TypeError):
            max_retries = 3

        retry_interval_value = self.get('RETRY_INTERVAL', '60')
        try:
            retry_interval = int(retry_interval_value)
        except (ValueError, TypeError):
            retry_interval = 60

        return {
            'flow_type': flow_type,
            'screenshot_enabled': screenshot_enabled,
            'log_level': log_level,
            'max_retries': max_retries,
            'retry_interval': retry_interval
        }

    def validate_config(self) -> bool:
        """
        验证配置的有效性
        
        Returns:
            bool: 配置是否有效
        """
        errors = []
        
        # 验证域名配置
        domain = self.get_domain()
        if not domain:
            errors.append("DOMAIN 未配置")
        
        # 验证邮箱配置
        if self.is_using_imap():
            imap_config = self.get_imap_config()
            required_imap_fields = ['server', 'port', 'username', 'password']
            
            for field in required_imap_fields:
                if not imap_config.get(field):
                    errors.append(f"IMAP_{field.upper()} 未配置")
        else:
            temp_config = self.get_temp_mail_config()
            if not temp_config['username']:
                errors.append("TEMP_MAIL 未配置")
        
        if errors:
            for error in errors:
                self.logger.error(f"配置验证失败: {error}")
            return False
        
        return True

    def print_config_summary(self):
        """打印配置摘要"""
        self.logger.info("=== 配置摘要 ===")
        
        # 邮箱配置
        if self.is_using_imap():
            imap_config = self.get_imap_config()
            self.logger.info(f"邮箱类型: IMAP/{imap_config['protocol']}")
            self.logger.info(f"IMAP服务器: {imap_config['server']}:{imap_config['port']}")
            self.logger.info(f"IMAP用户: {imap_config['username']}")
            self.logger.info(f"IMAP密码: {'*' * len(imap_config['password'])}")
        else:
            temp_config = self.get_temp_mail_config()
            self.logger.info(f"邮箱类型: 临时邮箱")
            self.logger.info(f"临时邮箱: {temp_config['username']}{temp_config['extension']}")
        
        self.logger.info(f"邮箱域名: {self.get_domain()}")
        
        # 浏览器配置
        browser_config = self.get_browser_config()
        self.logger.info(f"无头模式: {browser_config['headless']}")
        if browser_config['proxy']:
            self.logger.info(f"代理设置: {browser_config['proxy']}")
        
        # 应用配置
        app_config = self.get_app_config()
        self.logger.info(f"流程类型: {app_config['flow_type']}")
        self.logger.info(f"截图功能: {app_config['screenshot_enabled']}")
        self.logger.info(f"日志级别: {app_config['log_level']}")
        
        self.logger.info("===============")

    def get_all_config(self) -> Dict[str, Any]:
        """
        获取所有配置
        
        Returns:
            dict: 所有配置数据
        """
        return {
            'env_data': self.env_data,
            'config_data': self.config_data,
            'temp_mail': self.get_temp_mail_config(),
            'imap': self.get_imap_config(),
            'browser': self.get_browser_config(),
            'app': self.get_app_config()
        }


# 全局配置管理器实例
_global_config_manager = None


def get_config() -> ConfigManager:
    """
    获取全局配置管理器
    
    Returns:
        ConfigManager: 配置管理器实例
    """
    global _global_config_manager
    
    if _global_config_manager is None:
        _global_config_manager = ConfigManager()
    
    return _global_config_manager


def init_config(config_file: str = None, env_file: str = None) -> ConfigManager:
    """
    初始化全局配置管理器
    
    Args:
        config_file (str): 配置文件路径
        env_file (str): 环境变量文件路径
        
    Returns:
        ConfigManager: 配置管理器实例
    """
    global _global_config_manager
    
    _global_config_manager = ConfigManager(config_file, env_file)
    return _global_config_manager
