"""
日志管理模块
提供统一的日志配置和管理功能
"""
import logging
import os
import sys
from datetime import datetime
from typing import Optional


class LoggerManager:
    """日志管理器"""
    
    def __init__(self, name: str = "auto-register", log_dir: str = "logs"):
        """
        初始化日志管理器
        
        Args:
            name (str): 日志器名称
            log_dir (str): 日志目录
        """
        self.name = name
        self.log_dir = log_dir
        self.logger = None
        self._ensure_log_dir()

    def _ensure_log_dir(self):
        """确保日志目录存在"""
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)

    def setup_logger(self, level: str = "INFO", 
                    console_output: bool = True,
                    file_output: bool = True,
                    log_format: Optional[str] = None) -> logging.Logger:
        """
        设置日志器
        
        Args:
            level (str): 日志级别
            console_output (bool): 是否输出到控制台
            file_output (bool): 是否输出到文件
            log_format (str): 日志格式
            
        Returns:
            logging.Logger: 配置好的日志器
        """
        # 创建日志器
        self.logger = logging.getLogger(self.name)
        self.logger.setLevel(getattr(logging, level.upper()))
        
        # 清除已有的处理器
        self.logger.handlers.clear()
        
        # 设置日志格式
        if log_format is None:
            log_format = '%(asctime)s - %(levelname)s - %(message)s'
        
        formatter = logging.Formatter(log_format)
        
        # 控制台输出
        if console_output:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(getattr(logging, level.upper()))
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)
        
        # 文件输出
        if file_output:
            # 按日期创建日志文件
            today = datetime.now().strftime("%Y-%m-%d")
            log_file = os.path.join(self.log_dir, f"{today}.log")
            
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(getattr(logging, level.upper()))
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
        
        return self.logger

    def get_logger(self) -> logging.Logger:
        """
        获取日志器
        
        Returns:
            logging.Logger: 日志器实例
        """
        if self.logger is None:
            self.setup_logger()
        
        return self.logger

    def set_level(self, level: str):
        """
        设置日志级别
        
        Args:
            level (str): 日志级别
        """
        if self.logger:
            self.logger.setLevel(getattr(logging, level.upper()))
            for handler in self.logger.handlers:
                handler.setLevel(getattr(logging, level.upper()))

    def add_file_handler(self, filename: str, level: str = "INFO"):
        """
        添加文件处理器
        
        Args:
            filename (str): 文件名
            level (str): 日志级别
        """
        if not self.logger:
            self.setup_logger()
        
        log_file = os.path.join(self.log_dir, filename)
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(getattr(logging, level.upper()))
        
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        
        self.logger.addHandler(file_handler)

    def remove_console_output(self):
        """移除控制台输出"""
        if self.logger:
            handlers_to_remove = []
            for handler in self.logger.handlers:
                if isinstance(handler, logging.StreamHandler) and handler.stream == sys.stdout:
                    handlers_to_remove.append(handler)
            
            for handler in handlers_to_remove:
                self.logger.removeHandler(handler)

    def cleanup_old_logs(self, days: int = 30):
        """
        清理旧日志文件
        
        Args:
            days (int): 保留天数
        """
        import glob
        from datetime import timedelta
        
        cutoff_date = datetime.now() - timedelta(days=days)
        
        log_pattern = os.path.join(self.log_dir, "*.log")
        log_files = glob.glob(log_pattern)
        
        for log_file in log_files:
            try:
                file_time = datetime.fromtimestamp(os.path.getmtime(log_file))
                if file_time < cutoff_date:
                    os.remove(log_file)
                    print(f"删除旧日志文件: {log_file}")
            except Exception as e:
                print(f"删除日志文件失败 {log_file}: {e}")


# 全局日志管理器实例
_global_logger_manager = None


def get_logger(name: str = "auto-register") -> logging.Logger:
    """
    获取全局日志器
    
    Args:
        name (str): 日志器名称
        
    Returns:
        logging.Logger: 日志器实例
    """
    global _global_logger_manager
    
    if _global_logger_manager is None:
        _global_logger_manager = LoggerManager(name)
        _global_logger_manager.setup_logger()
    
    return _global_logger_manager.get_logger()


def setup_global_logger(level: str = "INFO", 
                       console_output: bool = True,
                       file_output: bool = True,
                       log_dir: str = "logs") -> logging.Logger:
    """
    设置全局日志器
    
    Args:
        level (str): 日志级别
        console_output (bool): 是否输出到控制台
        file_output (bool): 是否输出到文件
        log_dir (str): 日志目录
        
    Returns:
        logging.Logger: 配置好的日志器
    """
    global _global_logger_manager
    
    _global_logger_manager = LoggerManager("auto-register", log_dir)
    return _global_logger_manager.setup_logger(level, console_output, file_output)


def set_log_level(level: str):
    """
    设置全局日志级别
    
    Args:
        level (str): 日志级别
    """
    global _global_logger_manager
    
    if _global_logger_manager:
        _global_logger_manager.set_level(level)


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # 颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }

    def format(self, record):
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


def setup_colored_logger(name: str = "auto-register", 
                        level: str = "INFO",
                        log_dir: str = "logs") -> logging.Logger:
    """
    设置彩色日志器
    
    Args:
        name (str): 日志器名称
        level (str): 日志级别
        log_dir (str): 日志目录
        
    Returns:
        logging.Logger: 彩色日志器
    """
    logger_manager = LoggerManager(name, log_dir)
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))
    
    # 清除已有处理器
    logger.handlers.clear()
    
    # 控制台处理器（彩色）
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, level.upper()))
    
    colored_formatter = ColoredFormatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(colored_formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器（无颜色）
    logger_manager._ensure_log_dir()
    today = datetime.now().strftime("%Y-%m-%d")
    log_file = os.path.join(log_dir, f"{today}.log")
    
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(getattr(logging, level.upper()))
    
    file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(file_formatter)
    logger.addHandler(file_handler)
    
    return logger


# 便捷函数
def info(message: str):
    """记录信息日志"""
    get_logger().info(message)


def debug(message: str):
    """记录调试日志"""
    get_logger().debug(message)


def warning(message: str):
    """记录警告日志"""
    get_logger().warning(message)


def error(message: str):
    """记录错误日志"""
    get_logger().error(message)


def critical(message: str):
    """记录严重错误日志"""
    get_logger().critical(message)


# 兼容原有代码的logger对象
logger = get_logger()
