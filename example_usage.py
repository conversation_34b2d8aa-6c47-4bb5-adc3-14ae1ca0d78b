#!/usr/bin/env python3
"""
重构后架构使用示例
展示如何使用新的分层架构
"""

def example_step_layer_usage():
    """示例：如何使用 step 层"""
    print("📝 Step 层使用示例")
    print("-" * 30)
    
    # 模拟标签页对象
    class MockTab:
        def __init__(self):
            self.name = "mock_tab"
    
    mock_tab = MockTab()
    
    # 使用输入步骤
    from browser.step.input_step import InputStep
    input_step = InputStep(mock_tab)
    
    print("✅ InputStep 创建成功")
    print("   - 可用方法: input_email_with_selectors()")
    print("   - 可用方法: input_password_with_selectors()")
    print("   - 可用方法: input_name_with_selectors()")
    print("   - 可用方法: input_verification_code_with_selectors()")
    
    # 使用按钮步骤
    from browser.step.button_step import ButtonStep
    button_step = ButtonStep(mock_tab)
    
    print("✅ ButtonStep 创建成功")
    print("   - 可用方法: click_submit_with_selectors()")
    print("   - 可用方法: click_register_with_selectors()")
    print("   - 可用方法: click_login_with_selectors()")
    print("   - 可用方法: click_continue_with_selectors()")
    
    # 使用表单步骤
    from browser.step.form_step import FormStep
    form_step = FormStep(mock_tab)
    
    print("✅ FormStep 创建成功")
    print("   - 可用方法: fill_registration_form()")
    print("   - 可用方法: fill_login_form()")
    print("   - 可用方法: submit_form_with_retry()")
    print("   - 可用方法: complete_registration_flow()")


def example_automation_layer_usage():
    """示例：如何使用 automation 层"""
    print("\n🤖 Automation 层使用示例")
    print("-" * 30)
    
    # 模拟配置对象
    class MockConfig:
        def get_app_config(self):
            return {'flow_type': 'cursor'}
        
        def get_browser_config(self):
            return {'headless': True}
        
        def get_domain(self):
            return 'example.com'
    
    mock_config = MockConfig()
    
    # 使用 Cursor 自动化
    from automation.cursor import Cursor
    cursor_automation = Cursor(mock_config)
    
    print("✅ Cursor 自动化类创建成功")
    print("   - 主要方法: run(account_info)")
    print("   - 清理方法: cleanup()")
    
    # 使用 Augment 自动化
    from automation.augment import Augment
    augment_automation = Augment(mock_config)
    
    print("✅ Augment 自动化类创建成功")
    print("   - 主要方法: run(account_info)")
    print("   - 清理方法: cleanup()")


def example_main_app_usage():
    """示例：如何使用主应用"""
    print("\n🚀 主应用使用示例")
    print("-" * 30)
    
    from main import AutoRegisterApp
    
    app = AutoRegisterApp()
    print("✅ AutoRegisterApp 创建成功")
    
    # 显示支持的流程类型
    flow_types = list(app.automation_classes.keys())
    print(f"📋 支持的流程类型: {flow_types}")
    
    # 显示如何获取自动化类
    for flow_type in flow_types:
        automation_class = app._get_automation_class(flow_type)
        print(f"   - {flow_type}: {automation_class.__name__}")


def example_layer_interaction():
    """示例：层次间的交互"""
    print("\n🔗 层次交互示例")
    print("-" * 30)
    
    print("典型的调用流程:")
    print("1. main.py 接收命令行参数")
    print("2. 根据 flow_type 选择对应的 automation 类")
    print("3. automation 类初始化组件并调用 flow 层")
    print("4. flow 层编排业务流程，调用 step 层")
    print("5. step 层处理通用操作，调用 action 层")
    print("6. action 层执行基础的浏览器操作")
    
    print("\n代码示例:")
    print("```python")
    print("# 在 flow 层中使用 step 层")
    print("from browser.step import FormStep")
    print("form_step = FormStep(tab)")
    print("success = form_step.fill_registration_form(account_info)")
    print("")
    print("# 在 step 层中使用 action 层")
    print("from browser.action.input import InputAction")
    print("input_action = InputAction(tab)")
    print("success = input_action.input_email(selector, email)")
    print("```")


def example_extending_architecture():
    """示例：如何扩展架构"""
    print("\n🔧 架构扩展示例")
    print("-" * 30)
    
    print("添加新的自动化流程:")
    print("1. 创建 automation/new_service.py")
    print("2. 实现 NewService 类，包含 run() 方法")
    print("3. 在 main.py 中注册: 'new_service': NewService")
    print("")
    
    print("添加新的操作步骤:")
    print("1. 创建 browser/step/new_step.py")
    print("2. 实现通用操作，使用多选择器机制")
    print("3. 在 flow 层中调用新的步骤")
    print("")
    
    print("添加新的基础操作:")
    print("1. 创建 browser/action/new_action.py")
    print("2. 只实现基础的单选择器操作")
    print("3. 在 step 层中组合使用")


def main():
    """主函数"""
    print("🎯 重构后架构使用示例")
    print("=" * 50)
    
    example_step_layer_usage()
    example_automation_layer_usage()
    example_main_app_usage()
    example_layer_interaction()
    example_extending_architecture()
    
    print("\n" + "=" * 50)
    print("📚 更多信息请参考:")
    print("   - refector.md - 重构详细说明")
    print("   - test_refactor.py - 重构验证测试")
    print("   - main.py --help - 命令行帮助")


if __name__ == "__main__":
    main()
