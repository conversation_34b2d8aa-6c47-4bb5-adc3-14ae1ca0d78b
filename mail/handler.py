"""
邮箱处理器接口模块
定义邮箱处理的统一接口
"""
from abc import ABC, abstractmethod
from typing import Optional, Dict, List
from logger.logger import get_logger


class EmailHandler(ABC):
    """邮箱处理器抽象基类"""
    
    def __init__(self, email_address: str):
        """
        初始化邮箱处理器
        
        Args:
            email_address (str): 邮箱地址
        """
        self.email_address = email_address
        self.logger = get_logger(self.__class__.__name__)

    @abstractmethod
    def get_verification_code(self, max_retries: int = 5, retry_interval: int = 60) -> Optional[str]:
        """
        获取验证码
        
        Args:
            max_retries (int): 最大重试次数
            retry_interval (int): 重试间隔时间（秒）
            
        Returns:
            str: 验证码，获取失败返回None
        """
        pass

    @abstractmethod
    def get_latest_email(self) -> Optional[Dict]:
        """
        获取最新邮件
        
        Returns:
            dict: 邮件信息，包含subject, body, sender等，获取失败返回None
        """
        pass

    @abstractmethod
    def delete_email(self, email_id: str) -> bool:
        """
        删除指定邮件
        
        Args:
            email_id (str): 邮件ID
            
        Returns:
            bool: 是否删除成功
        """
        pass

    @abstractmethod
    def cleanup(self) -> bool:
        """
        清理邮箱（删除所有邮件）
        
        Returns:
            bool: 是否清理成功
        """
        pass

    def extract_verification_code(self, text: str) -> Optional[str]:
        """
        从文本中提取验证码，支持cursor和augment格式

        Args:
            text (str): 文本内容

        Returns:
            str: 验证码，未找到返回None
        """
        import re

        # 常见的验证码模式，包括augment格式
        patterns = [
            r'Your verification code is:\s*(\d{6})',  # Augment格式
            r'verification code is:\s*(\d{6})',       # 通用格式
            r'code is:\s*(\d{6})',                    # 简化格式
            r'验证码[：:]\s*(\d{4,6})',                # 中文验证码格式
            r'verification code[：:]\s*(\d{4,6})',    # 英文验证码格式
            r'code[：:]\s*(\d{4,6})',                 # 简单code格式
            r'\b\d{6}\b',                             # 6位数字
            r'\b\d{4}\b',                             # 4位数字
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                for match in matches:
                    code = match if isinstance(match, str) else match
                    if len(code) >= 4 and len(code) <= 6 and code.isdigit():
                        self.logger.info(f"提取到验证码: {code}")
                        return code

        self.logger.warning("未能从文本中提取验证码")
        return None

    def is_verification_email(self, subject: str, sender: str, body: str = "") -> bool:
        """
        判断是否为验证邮件
        
        Args:
            subject (str): 邮件主题
            sender (str): 发件人
            body (str): 邮件正文
            
        Returns:
            bool: 是否为验证邮件
        """
        # 验证邮件的关键词
        verification_keywords = [
            'verification', 'verify', 'confirm', 'code',
            '验证', '确认', '验证码', 'otp', 'pin'
        ]
        
        # 检查主题
        subject_lower = subject.lower()
        for keyword in verification_keywords:
            if keyword in subject_lower:
                return True
        
        # 检查发件人（支持cursor和augment）
        trusted_senders = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',  # Augment发件人
            '<EMAIL>',
            'verification@',
            'noreply@',
            'no-reply@'
        ]
        
        sender_lower = sender.lower()
        for trusted in trusted_senders:
            if trusted in sender_lower:
                return True
        
        # 检查正文（如果提供）
        if body:
            body_lower = body.lower()
            for keyword in verification_keywords:
                if keyword in body_lower:
                    return True
        
        return False

    def wait_for_verification_email(self, timeout: int = 300, check_interval: int = 10) -> Optional[str]:
        """
        等待验证邮件并提取验证码
        
        Args:
            timeout (int): 超时时间（秒）
            check_interval (int): 检查间隔（秒）
            
        Returns:
            str: 验证码，超时或失败返回None
        """
        import time
        
        start_time = time.time()
        self.logger.info(f"开始等待验证邮件，超时时间: {timeout}秒")
        
        while time.time() - start_time < timeout:
            try:
                # 获取最新邮件
                email = self.get_latest_email()
                if email:
                    subject = email.get('subject', '')
                    sender = email.get('sender', '')
                    body = email.get('body', '')
                    
                    # 检查是否为验证邮件
                    if self.is_verification_email(subject, sender, body):
                        self.logger.info(f"收到验证邮件: {subject}")
                        
                        # 提取验证码
                        code = self.extract_verification_code(body)
                        if code:
                            return code
                
                # 等待下次检查
                time.sleep(check_interval)
                
            except Exception as e:
                self.logger.error(f"检查邮件时出错: {e}")
                time.sleep(check_interval)
        
        self.logger.warning("等待验证邮件超时")
        return None

    def get_email_list(self, limit: int = 10) -> List[Dict]:
        """
        获取邮件列表
        
        Args:
            limit (int): 获取邮件数量限制
            
        Returns:
            list: 邮件列表
        """
        # 默认实现，子类可以重写
        try:
            latest = self.get_latest_email()
            return [latest] if latest else []
        except Exception as e:
            self.logger.error(f"获取邮件列表失败: {e}")
            return []

    def validate_email_address(self) -> bool:
        """
        验证邮箱地址格式
        
        Returns:
            bool: 邮箱地址是否有效
        """
        import re
        
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        is_valid = bool(re.match(pattern, self.email_address))
        
        if not is_valid:
            self.logger.error(f"无效的邮箱地址: {self.email_address}")
        
        return is_valid

    def __str__(self):
        return f"{self.__class__.__name__}({self.email_address})"

    def __repr__(self):
        return self.__str__()


class EmailHandlerFactory:
    """邮箱处理器工厂类"""
    
    @staticmethod
    def create_handler(email_type: str, email_address: str, **kwargs) -> EmailHandler:
        """
        创建邮箱处理器
        
        Args:
            email_type (str): 邮箱类型 ('imap', 'temp', 'pop3')
            email_address (str): 邮箱地址
            **kwargs: 其他配置参数
            
        Returns:
            EmailHandler: 邮箱处理器实例
            
        Raises:
            ValueError: 不支持的邮箱类型
        """
        if email_type.lower() == 'imap':
            from .imap import IMAPEmailHandler
            return IMAPEmailHandler(email_address, **kwargs)
        elif email_type.lower() == 'temp':
            from .temp import TempEmailHandler
            return TempEmailHandler(email_address, **kwargs)
        elif email_type.lower() == 'pop3':
            from .imap import POP3EmailHandler
            return POP3EmailHandler(email_address, **kwargs)
        else:
            raise ValueError(f"不支持的邮箱类型: {email_type}")

    @staticmethod
    def create_from_config(config, service_type: str = 'cursor') -> EmailHandler:
        """
        从配置创建邮箱处理器

        Args:
            config: 配置对象
            service_type (str): 服务类型，'cursor' 或 'augment'

        Returns:
            EmailHandler: 邮箱处理器实例
        """
        # 根据配置判断使用哪种邮箱处理器
        if config.is_using_imap():
            # 使用IMAP
            imap_config = config.get_imap_config()
            protocol = config.get_protocol()

            if protocol.upper() == 'POP3':
                from .imap import POP3EmailHandler
                return POP3EmailHandler(
                    email_address=imap_config['username'],
                    server=imap_config['server'],
                    port=int(imap_config['port']),
                    username=imap_config['username'],
                    password=imap_config['password'],
                    service_type=service_type
                )
            else:
                from .imap import IMAPEmailHandler
                return IMAPEmailHandler(
                    email_address=imap_config['username'],
                    server=imap_config['server'],
                    port=int(imap_config['port']),
                    username=imap_config['username'],
                    password=imap_config['password'],
                    folder=imap_config.get('folder', 'INBOX'),
                    service_type=service_type
                )
        else:
            # 使用临时邮箱
            from .temp import TempEmailHandler
            temp_config = config.get_temp_mail_config()
            return TempEmailHandler(
                email_address=f"{temp_config['username']}{temp_config['extension']}",
                username=temp_config['username'],
                epin=temp_config['epin'],
                extension=temp_config['extension'],
                service_type=service_type
            )
