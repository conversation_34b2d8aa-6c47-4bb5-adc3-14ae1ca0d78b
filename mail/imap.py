"""
IMAP邮箱处理模块
支持IMAP和POP3协议的邮箱处理，根据配置验证cursor或augment的邮件
"""
import imaplib
import poplib
import email
import time
import re
from datetime import datetime
from typing import Optional, Dict
from email.parser import Parser
from .handler import EmailHandler


class IMAPEmailHandler(EmailHandler):
    """IMAP邮箱处理器"""

    def __init__(self, email_address: str, server: str, port: int,
                 username: str, password: str, folder: str = 'INBOX',
                 service_type: str = 'cursor'):
        """
        初始化IMAP邮箱处理器

        Args:
            email_address (str): 邮箱地址
            server (str): IMAP服务器地址
            port (int): IMAP服务器端口
            username (str): 用户名
            password (str): 密码
            folder (str): 邮箱文件夹，默认为INBOX
            service_type (str): 服务类型，'cursor' 或 'augment'
        """
        super().__init__(email_address)
        self.server = server
        self.port = port
        self.username = username
        self.password = password
        self.folder = folder
        self.service_type = service_type.lower()
        self.connection = None

    def _connect(self) -> bool:
        """连接到IMAP服务器"""
        try:
            if self.connection:
                return True

            self.connection = imaplib.IMAP4_SSL(self.server, self.port)
            self.connection.login(self.username, self.password)

            # 针对网易系邮箱的特殊处理
            if self.username.endswith(('@163.com', '@126.com', '@yeah.net')):
                imap_id = ("name", self.username.split('@')[0],
                          "contact", self.username, "version", "1.0.0",
                          "vendor", "imaplib")
                self.connection.xatom('ID', '("' + '" "'.join(imap_id) + '")')

            self.connection.select(self.folder)
            return True

        except Exception as e:
            self.logger.error(f"连接IMAP服务器失败: {e}")
            self.connection = None
            return False

    def _disconnect(self):
        """断开IMAP连接"""
        if self.connection:
            try:
                self.connection.logout()
            except Exception:
                pass
            finally:
                self.connection = None

    def get_verification_code(self, max_retries: int = 5, retry_interval: int = 60) -> Optional[str]:
        """
        获取验证码，支持cursor和augment两种服务

        Args:
            max_retries (int): 最大重试次数
            retry_interval (int): 重试间隔时间（秒）

        Returns:
            str: 验证码，获取失败返回None
        """
        for attempt in range(max_retries):
            try:
                self.logger.info(f"尝试获取{self.service_type}验证码 (第 {attempt + 1}/{max_retries} 次)...")

                code = self._get_mail_code_by_imap()
                if code:
                    return code

                if attempt < max_retries - 1:
                    self.logger.warning(f"未获取到验证码，{retry_interval} 秒后重试...")
                    time.sleep(retry_interval)

            except Exception as e:
                self.logger.error(f"获取验证码异常: {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_interval)

        self.logger.error("获取验证码失败，已达到最大重试次数")
        return None

    def _get_mail_code_by_imap(self, retry: int = 0) -> Optional[str]:
        """
        通过IMAP获取邮件验证码，支持cursor和augment

        Args:
            retry (int): 重试次数

        Returns:
            str: 验证码，获取失败返回None
        """
        if retry > 0:
            time.sleep(3)
        if retry >= 20:
            raise Exception("获取验证码超时")

        try:
            if not self._connect():
                return None

            # 根据服务类型确定搜索策略
            search_by_date = self.username.endswith(('@163.com', '@126.com', '@yeah.net'))

            if search_by_date:
                # 网易系邮箱按日期搜索未读邮件
                date = datetime.now().strftime("%d-%b-%Y")
                status, mail_ids = self.connection.search(None, f'(UNSEEN SINCE "{date}")')
            else:
                # 根据服务类型搜索特定发件人的邮件
                if self.service_type == 'cursor':
                    status, mail_ids = self.connection.search(None, '(UNSEEN FROM "<EMAIL>")')
                elif self.service_type == 'augment':
                    status, mail_ids = self.connection.search(None, '(UNSEEN FROM "<EMAIL>")')
                else:
                    # 默认搜索收件人邮箱
                    status, mail_ids = self.connection.search(None, f'(UNSEEN TO "{self.email_address}")')

            if status != 'OK' or not mail_ids[0]:
                return self._get_mail_code_by_imap(retry=retry + 1)

            mail_ids = mail_ids[0].split()

            # 从最新邮件开始检查
            for mail_id in reversed(mail_ids):
                status, msg_data = self.connection.fetch(mail_id, '(RFC822)')
                if status != 'OK':
                    continue

                raw_email = msg_data[0][1]
                email_message = email.message_from_bytes(raw_email)

                # 如果是按日期搜索，需要验证收件人
                if search_by_date and email_message['to'] != self.email_address:
                    continue

                # 验证发件人是否匹配服务类型
                sender = email_message.get('from', '').lower()
                if not self._is_valid_sender(sender):
                    continue

                body = self._extract_email_body(email_message)
                if body:
                    # 避免邮箱地址被误识别为验证码
                    body = body.replace(self.email_address, '')
                    code = self._extract_verification_code_by_service(body)
                    if code:
                        # 删除已处理的邮件
                        self.connection.store(mail_id, '+FLAGS', '\\Deleted')
                        self.connection.expunge()
                        return code

            return self._get_mail_code_by_imap(retry=retry + 1)

        except Exception as e:
            self.logger.error(f"IMAP获取邮件失败: {e}")
            return None
        finally:
            self._disconnect()

    def _is_valid_sender(self, sender: str) -> bool:
        """
        验证发件人是否匹配服务类型

        Args:
            sender (str): 发件人邮箱地址

        Returns:
            bool: 是否为有效发件人
        """
        if self.service_type == 'cursor':
            return '<EMAIL>' in sender or 'cursor.sh' in sender
        elif self.service_type == 'augment':
            return '<EMAIL>' in sender or 'augmentcode.com' in sender
        else:
            # 默认接受所有发件人
            return True

    def _extract_verification_code_by_service(self, body: str) -> Optional[str]:
        """
        根据服务类型提取验证码

        Args:
            body (str): 邮件正文

        Returns:
            str: 验证码，未找到返回None
        """
        if self.service_type == 'cursor':
            # Cursor使用6位数字验证码
            code_match = re.search(r'\b\d{6}\b', body)
            if code_match:
                return code_match.group()
        elif self.service_type == 'augment':
            # Augment格式：Your verification code is: 553657
            patterns = [
                r'Your verification code is:\s*(\d{6})',
                r'verification code is:\s*(\d{6})',
                r'code is:\s*(\d{6})',
                r'\b\d{6}\b'  # 备用：6位数字
            ]
            for pattern in patterns:
                code_match = re.search(pattern, body, re.IGNORECASE)
                if code_match:
                    return code_match.group(1) if code_match.groups() else code_match.group()
        else:
            # 默认使用通用验证码提取
            return self.extract_verification_code(body)

        return None

    def _extract_email_body(self, email_message) -> Optional[str]:
        """提取邮件正文"""
        try:
            if email_message.is_multipart():
                for part in email_message.walk():
                    content_type = part.get_content_type()
                    content_disposition = str(part.get("Content-Disposition"))
                    if content_type == "text/plain" and "attachment" not in content_disposition:
                        charset = part.get_content_charset() or 'utf-8'
                        return part.get_payload(decode=True).decode(charset, errors='ignore')
            else:
                content_type = email_message.get_content_type()
                if content_type == "text/plain":
                    charset = email_message.get_content_charset() or 'utf-8'
                    return email_message.get_payload(decode=True).decode(charset, errors='ignore')
        except Exception as e:
            self.logger.error(f"提取邮件正文失败: {e}")

        return None

    def get_latest_email(self) -> Optional[Dict]:
        """获取最新邮件"""
        try:
            if not self._connect():
                return None

            status, mail_ids = self.connection.search(None, 'ALL')
            if status != 'OK' or not mail_ids[0]:
                return None

            mail_ids = mail_ids[0].split()
            if not mail_ids:
                return None

            latest_id = mail_ids[-1]
            status, msg_data = self.connection.fetch(latest_id, '(RFC822)')
            if status != 'OK':
                return None

            raw_email = msg_data[0][1]
            email_message = email.message_from_bytes(raw_email)

            return {
                'id': latest_id.decode(),
                'subject': email_message['subject'] or '',
                'sender': email_message['from'] or '',
                'body': self._extract_email_body(email_message) or '',
                'date': email_message['date'] or ''
            }

        except Exception as e:
            self.logger.error(f"获取最新邮件失败: {e}")
            return None
        finally:
            self._disconnect()

    def delete_email(self, email_id: str) -> bool:
        """删除指定邮件"""
        try:
            if not self._connect():
                return False

            self.connection.store(email_id, '+FLAGS', '\\Deleted')
            self.connection.expunge()
            return True

        except Exception as e:
            self.logger.error(f"删除邮件失败: {e}")
            return False
        finally:
            self._disconnect()

    def cleanup(self) -> bool:
        """清理邮箱（删除所有邮件）"""
        try:
            if not self._connect():
                return False

            status, mail_ids = self.connection.search(None, 'ALL')
            if status == 'OK' and mail_ids[0]:
                for mail_id in mail_ids[0].split():
                    self.connection.store(mail_id, '+FLAGS', '\\Deleted')
                self.connection.expunge()

            return True

        except Exception as e:
            self.logger.error(f"清理邮箱失败: {e}")
            return False
        finally:
            self._disconnect()


class POP3EmailHandler(EmailHandler):
    """POP3邮箱处理器"""

    def __init__(self, email_address: str, server: str, port: int,
                 username: str, password: str, service_type: str = 'cursor'):
        """
        初始化POP3邮箱处理器

        Args:
            email_address (str): 邮箱地址
            server (str): POP3服务器地址
            port (int): POP3服务器端口
            username (str): 用户名
            password (str): 密码
            service_type (str): 服务类型，'cursor' 或 'augment'
        """
        super().__init__(email_address)
        self.server = server
        self.port = port
        self.username = username
        self.password = password
        self.service_type = service_type.lower()

    def get_verification_code(self, max_retries: int = 5, retry_interval: int = 60) -> Optional[str]:
        """获取验证码，支持cursor和augment两种服务"""
        for attempt in range(max_retries):
            try:
                self.logger.info(f"尝试获取{self.service_type}验证码 (第 {attempt + 1}/{max_retries} 次)...")

                code = self._get_mail_code_by_pop3()
                if code:
                    return code

                if attempt < max_retries - 1:
                    self.logger.warning(f"未获取到验证码，{retry_interval} 秒后重试...")
                    time.sleep(retry_interval)

            except Exception as e:
                self.logger.error(f"获取验证码异常: {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_interval)

        self.logger.error("获取验证码失败，已达到最大重试次数")
        return None

    def _get_mail_code_by_pop3(self, retry: int = 0) -> Optional[str]:
        """通过POP3获取邮件验证码，支持cursor和augment"""
        if retry > 0:
            time.sleep(3)
        if retry >= 20:
            raise Exception("获取验证码超时")

        pop3 = None
        try:
            pop3 = poplib.POP3_SSL(self.server, self.port)
            pop3.user(self.username)
            pop3.pass_(self.password)

            # 获取最新的10封邮件
            num_messages = len(pop3.list()[1])
            for i in range(num_messages, max(1, num_messages-9), -1):
                response, lines, octets = pop3.retr(i)
                msg_content = b'\r\n'.join(lines).decode('utf-8')
                msg = Parser().parsestr(msg_content)

                # 根据服务类型检查发件人
                sender = msg.get('From', '').lower()
                if self._is_valid_pop3_sender(sender):
                    body = self._extract_pop3_body(msg)
                    if body:
                        code = self._extract_verification_code_by_service(body)
                        if code:
                            pop3.quit()
                            return code

            pop3.quit()
            return self._get_mail_code_by_pop3(retry=retry + 1)

        except Exception as e:
            self.logger.error(f"POP3获取邮件失败: {e}")
            if pop3:
                try:
                    pop3.quit()
                except:
                    pass
            return None

    def _is_valid_pop3_sender(self, sender: str) -> bool:
        """验证POP3发件人是否匹配服务类型"""
        if self.service_type == 'cursor':
            return '<EMAIL>' in sender
        elif self.service_type == 'augment':
            return '<EMAIL>' in sender
        else:
            return True

    def _extract_pop3_body(self, msg) -> Optional[str]:
        """提取POP3邮件正文"""
        try:
            if msg.is_multipart():
                for part in msg.walk():
                    content_type = part.get_content_type()
                    content_disposition = str(part.get("Content-Disposition"))
                    if content_type == "text/plain" and "attachment" not in content_disposition:
                        return part.get_payload(decode=True).decode('utf-8', errors='ignore')
            else:
                return msg.get_payload(decode=True).decode('utf-8', errors='ignore')
        except Exception as e:
            self.logger.error(f"提取POP3邮件正文失败: {e}")
        return None

    def get_latest_email(self) -> Optional[Dict]:
        """获取最新邮件"""
        pop3 = None
        try:
            pop3 = poplib.POP3_SSL(self.server, self.port)
            pop3.user(self.username)
            pop3.pass_(self.password)

            num_messages = len(pop3.list()[1])
            if num_messages == 0:
                return None

            response, lines, octets = pop3.retr(num_messages)
            msg_content = b'\r\n'.join(lines).decode('utf-8')
            msg = Parser().parsestr(msg_content)

            return {
                'id': str(num_messages),
                'subject': msg.get('subject', ''),
                'sender': msg.get('from', ''),
                'body': self._extract_pop3_body(msg) or '',
                'date': msg.get('date', '')
            }

        except Exception as e:
            self.logger.error(f"获取最新邮件失败: {e}")
            return None
        finally:
            if pop3:
                try:
                    pop3.quit()
                except:
                    pass

    def delete_email(self, email_id: str) -> bool:
        """删除指定邮件"""
        pop3 = None
        try:
            pop3 = poplib.POP3_SSL(self.server, self.port)
            pop3.user(self.username)
            pop3.pass_(self.password)
            pop3.dele(int(email_id))
            pop3.quit()
            return True
        except Exception as e:
            self.logger.error(f"删除邮件失败: {e}")
            if pop3:
                try:
                    pop3.quit()
                except:
                    pass
            return False

    def cleanup(self) -> bool:
        """清理邮箱（删除所有邮件）"""
        pop3 = None
        try:
            pop3 = poplib.POP3_SSL(self.server, self.port)
            pop3.user(self.username)
            pop3.pass_(self.password)

            num_messages = len(pop3.list()[1])
            for i in range(1, num_messages + 1):
                pop3.dele(i)

            pop3.quit()
            return True
        except Exception as e:
            self.logger.error(f"清理邮箱失败: {e}")
            if pop3:
                try:
                    pop3.quit()
                except:
                    pass
            return False
