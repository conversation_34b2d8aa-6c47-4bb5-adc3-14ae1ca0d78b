"""
临时邮箱处理模块
支持tempmail.plus等临时邮箱服务
"""
import time
import requests
from typing import Optional, Dict, List
from logger.logger import get_logger
from .handler import EmailHandler


class TempEmailHandler(EmailHandler):
    """临时邮箱处理器"""
    
    def __init__(self, email_address: str, username: str, epin: str, extension: str,
                 service_type: str = 'cursor'):
        """
        初始化临时邮箱处理器

        Args:
            email_address (str): 完整邮箱地址
            username (str): 用户名部分
            epin (str): 邮箱PIN码
            extension (str): 邮箱后缀
            service_type (str): 服务类型，'cursor' 或 'augment'
        """
        super().__init__(email_address)
        self.username = username
        self.epin = epin
        self.extension = extension
        self.service_type = service_type.lower()
        self.session = requests.Session()

        # API配置
        self.base_url = "https://tempmail.plus/api"
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

    def get_verification_code(self, max_retries: int = 5, retry_interval: int = 60) -> Optional[str]:
        """
        获取验证码
        
        Args:
            max_retries (int): 最大重试次数
            retry_interval (int): 重试间隔时间（秒）
            
        Returns:
            str: 验证码，获取失败返回None
        """
        for attempt in range(max_retries):
            try:
                self.logger.info(f"尝试获取{self.service_type}验证码 (第 {attempt + 1}/{max_retries} 次)...")
                
                verify_code, first_id = self._get_latest_mail_code()
                if verify_code and first_id:
                    # 删除已处理的邮件
                    self._cleanup_mail(first_id)
                    return verify_code
                
                if attempt < max_retries - 1:
                    self.logger.warning(f"未获取到验证码，{retry_interval} 秒后重试...")
                    time.sleep(retry_interval)
                    
            except Exception as e:
                self.logger.error(f"获取验证码异常: {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_interval)
        
        self.logger.error("获取验证码失败，已达到最大重试次数")
        return None

    def _get_latest_mail_code(self) -> tuple[Optional[str], Optional[str]]:
        """
        获取最新邮件中的验证码
        
        Returns:
            tuple: (验证码, 邮件ID)，获取失败返回(None, None)
        """
        try:
            # 获取邮件列表
            mail_list_url = f"{self.base_url}/mails"
            params = {
                'email': f"{self.username}{self.extension}",
                'limit': 20,
                'epin': self.epin
            }
            
            response = self.session.get(mail_list_url, params=params)
            response.raise_for_status()
            mail_list_data = response.json()
            
            time.sleep(0.5)  # 避免请求过快
            
            if not mail_list_data.get("result"):
                self.logger.debug("邮件列表为空")
                return None, None

            # 获取最新邮件的ID
            first_id = mail_list_data.get("first_id")
            if not first_id:
                self.logger.debug("未找到邮件ID")
                return None, None

            # 获取具体邮件内容
            mail_detail_url = f"{self.base_url}/mails/{first_id}"
            params = {
                'email': f"{self.username}{self.extension}",
                'epin': self.epin
            }
            
            response = self.session.get(mail_detail_url, params=params)
            response.raise_for_status()
            mail_detail_data = response.json()
            
            time.sleep(0.5)  # 避免请求过快
            
            if not mail_detail_data.get("result"):
                self.logger.debug("邮件详情获取失败")
                return None, None

            # 从邮件文本中提取验证码
            mail_text = mail_detail_data.get("text", "")
            mail_subject = mail_detail_data.get("subject", "")
            
            self.logger.info(f"找到邮件主题: {mail_subject}")
            
            # 根据服务类型提取验证码
            code = self._extract_verification_code_by_service(mail_text, mail_subject)
            if code:
                return code, first_id
            
            return None, None

        except requests.RequestException as e:
            self.logger.error(f"请求邮件API失败: {e}")
            return None, None
        except Exception as e:
            self.logger.error(f"获取邮件验证码失败: {e}")
            return None, None

    def _extract_verification_code_by_service(self, mail_text: str, mail_subject: str = "") -> Optional[str]:
        """
        根据服务类型提取验证码

        Args:
            mail_text (str): 邮件正文
            mail_subject (str): 邮件主题

        Returns:
            str: 验证码，未找到返回None
        """
        import re

        # 合并邮件主题和正文进行搜索
        full_text = f"{mail_subject} {mail_text}"

        if self.service_type == 'cursor':
            # Cursor使用6位数字验证码
            code_match = re.search(r'\b\d{6}\b', full_text)
            if code_match:
                return code_match.group()
        elif self.service_type == 'augment':
            # Augment格式：Your verification code is: 553657
            patterns = [
                r'Your verification code is:\s*(\d{6})',
                r'verification code is:\s*(\d{6})',
                r'code is:\s*(\d{6})',
                r'\b\d{6}\b'  # 备用：6位数字
            ]
            for pattern in patterns:
                code_match = re.search(pattern, full_text, re.IGNORECASE)
                if code_match:
                    code = code_match.group(1) if code_match.groups() else code_match.group()
                    self.logger.info(f"从{self.service_type}邮件中提取到验证码: {code}")
                    return code
        else:
            # 默认使用通用验证码提取
            return self.extract_verification_code(full_text)

        self.logger.warning(f"未能从{self.service_type}邮件中提取验证码")
        return None

    def get_latest_email(self) -> Optional[Dict]:
        """
        获取最新邮件
        
        Returns:
            dict: 邮件信息
        """
        try:
            # 获取邮件列表
            mail_list_url = f"{self.base_url}/mails"
            params = {
                'email': f"{self.username}{self.extension}",
                'limit': 1,
                'epin': self.epin
            }
            
            response = self.session.get(mail_list_url, params=params)
            response.raise_for_status()
            mail_list_data = response.json()
            
            if not mail_list_data.get("result"):
                return None

            first_id = mail_list_data.get("first_id")
            if not first_id:
                return None

            # 获取邮件详情
            mail_detail_url = f"{self.base_url}/mails/{first_id}"
            params = {
                'email': f"{self.username}{self.extension}",
                'epin': self.epin
            }
            
            response = self.session.get(mail_detail_url, params=params)
            response.raise_for_status()
            mail_detail_data = response.json()
            
            if not mail_detail_data.get("result"):
                return None

            return {
                'id': first_id,
                'subject': mail_detail_data.get('subject', ''),
                'sender': mail_detail_data.get('from', ''),
                'body': mail_detail_data.get('text', ''),
                'date': mail_detail_data.get('date', '')
            }
            
        except Exception as e:
            self.logger.error(f"获取最新邮件失败: {e}")
            return None

    def delete_email(self, email_id: str) -> bool:
        """
        删除指定邮件
        
        Args:
            email_id (str): 邮件ID
            
        Returns:
            bool: 是否删除成功
        """
        return self._cleanup_mail(email_id)

    def _cleanup_mail(self, first_id: str) -> bool:
        """
        删除指定邮件
        
        Args:
            first_id (str): 邮件ID
            
        Returns:
            bool: 是否删除成功
        """
        try:
            delete_url = f"{self.base_url}/mails/"
            payload = {
                "email": f"{self.username}{self.extension}",
                "first_id": first_id,
                "epin": self.epin,
            }

            # 最多尝试5次
            for attempt in range(5):
                response = self.session.delete(delete_url, data=payload)
                
                try:
                    result = response.json().get("result")
                    if result is True:
                        self.logger.info(f"成功删除邮件: {first_id}")
                        return True
                except:
                    pass

                # 如果失败,等待0.5秒后重试
                if attempt < 4:
                    time.sleep(0.5)

            self.logger.warning(f"删除邮件失败: {first_id}")
            return False
            
        except Exception as e:
            self.logger.error(f"删除邮件异常: {e}")
            return False

    def cleanup(self) -> bool:
        """
        清理邮箱（删除所有邮件）
        
        Returns:
            bool: 是否清理成功
        """
        try:
            # 获取所有邮件
            mail_list_url = f"{self.base_url}/mails"
            params = {
                'email': f"{self.username}{self.extension}",
                'limit': 100,  # 获取更多邮件
                'epin': self.epin
            }
            
            response = self.session.get(mail_list_url, params=params)
            response.raise_for_status()
            mail_list_data = response.json()
            
            if not mail_list_data.get("result"):
                self.logger.info("邮箱已为空")
                return True

            # 删除所有邮件
            success_count = 0
            total_count = 0
            
            # 这里简化处理，实际可能需要分页获取所有邮件
            first_id = mail_list_data.get("first_id")
            if first_id:
                total_count = 1
                if self._cleanup_mail(first_id):
                    success_count = 1

            self.logger.info(f"清理邮箱完成: {success_count}/{total_count}")
            return success_count == total_count
            
        except Exception as e:
            self.logger.error(f"清理邮箱失败: {e}")
            return False

    def get_email_list(self, limit: int = 10) -> List[Dict]:
        """
        获取邮件列表
        
        Args:
            limit (int): 获取邮件数量限制
            
        Returns:
            list: 邮件列表
        """
        try:
            mail_list_url = f"{self.base_url}/mails"
            params = {
                'email': f"{self.username}{self.extension}",
                'limit': limit,
                'epin': self.epin
            }
            
            response = self.session.get(mail_list_url, params=params)
            response.raise_for_status()
            mail_list_data = response.json()
            
            if not mail_list_data.get("result"):
                return []

            # 这里简化处理，实际API可能返回邮件列表
            # 根据实际API响应格式调整
            emails = mail_list_data.get("emails", [])
            return emails
            
        except Exception as e:
            self.logger.error(f"获取邮件列表失败: {e}")
            return []

    def is_email_available(self) -> bool:
        """
        检查邮箱是否可用
        
        Returns:
            bool: 邮箱是否可用
        """
        try:
            # 尝试获取邮件列表来验证邮箱是否可用
            mail_list_url = f"{self.base_url}/mails"
            params = {
                'email': f"{self.username}{self.extension}",
                'limit': 1,
                'epin': self.epin
            }
            
            response = self.session.get(mail_list_url, params=params, timeout=10)
            response.raise_for_status()
            
            # 如果请求成功，说明邮箱可用
            return True
            
        except Exception as e:
            self.logger.error(f"检查邮箱可用性失败: {e}")
            return False
