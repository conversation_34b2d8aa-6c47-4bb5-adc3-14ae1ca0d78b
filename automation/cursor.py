"""
Cursor 自动化注册模块
实现 Cursor 账号注册的完整业务流程
"""
import time
from typing import Dict, Optional
from browser.manager import BrowserManager
from browser.flow.cursor import CursorRegistrationFlow
from generator.account import AccountGenerator
from mail.handler import EmailHandlerFactory
from logger.logger import get_logger


class Cursor:
    """Cursor 自动化注册类"""
    
    def __init__(self, config):
        """
        初始化 Cursor 自动化注册
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.logger = get_logger("Cursor")
        self.browser_manager = None
        self.email_handler = None
        self.account_generator = None
        
    def _initialize_components(self):
        """初始化组件"""
        try:
            # 初始化浏览器管理器
            self.browser_manager = BrowserManager()
            
            # 初始化邮箱处理器
            self.email_handler = EmailHandlerFactory.create_from_config(self.config, 'cursor')
            
            # 初始化账号生成器
            domain = self.config.get_domain()
            self.account_generator = AccountGenerator(domain=domain, config=self.config)
            
            self.logger.info("Cursor 组件初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"Cursor 组件初始化失败: {e}")
            return False
    
    def run(self, account_info: dict = None) -> bool:
        """
        运行 Cursor 注册流程
        
        Args:
            account_info (dict): 账号信息，如果为None则自动生成
            
        Returns:
            bool: 注册是否成功
        """
        try:
            self.logger.info("开始 Cursor 注册流程")
            
            # 初始化组件
            if not self._initialize_components():
                return False
            
            # 生成或使用提供的账号信息
            if not account_info:
                account_info = self.account_generator.generate_account_info()
            
            # 验证账号信息
            if not self.account_generator.validate_account_info(account_info):
                self.logger.error("账号信息验证失败")
                return False
            
            # 初始化浏览器
            browser_config = self.config.get_browser_config()
            browser = self.browser_manager.init_browser(
                headless=browser_config['headless']
            )
            
            if not browser:
                self.logger.error("浏览器初始化失败")
                return False
            
            try:
                # 获取标签页
                tab = browser.latest_tab
                
                # 创建注册流程
                registration_flow = CursorRegistrationFlow(tab, self.config)
                
                # 执行注册
                success = registration_flow.register_account(account_info)
                
                if success:
                    self.logger.info("Cursor 注册成功")
                    
                    # 获取会话令牌
                    token = registration_flow.get_session_token()
                    if token:
                        self.logger.info("成功获取会话令牌")
                        # 这里可以保存令牌或进行其他处理
                    
                    return True
                else:
                    self.logger.error("Cursor 注册失败")
                    return False
                    
            finally:
                # 清理浏览器资源
                if self.browser_manager:
                    self.browser_manager.quit()
                
        except Exception as e:
            self.logger.error(f"Cursor 注册流程异常: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        if self.browser_manager:
            self.browser_manager.quit()
