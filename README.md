# 自动注册程序

基于二层架构的浏览器自动化注册程序，支持Cursor和Augment两种注册/登录流程。

## 项目结构

```
auto-register/
├── browser/               # 浏览器自动化
│   ├── automation/        # 二层架构自动化
│   │   ├── action/        # Action层 - 浏览器基础元素操作
│   │   │   ├── input.py   # 输入框操作
│   │   │   ├── button.py  # 按钮操作
│   │   │   ├── navigate.py # 页面导航
│   │   │   └── turnstile.py # Turnstile验证
│   │   └── flow/          # Flow层 - 业务流程
│   │       ├── augment.py # Augment登录流程
│   │       └── cursor.py  # Cursor注册流程
│   ├── manager.py         # 浏览器管理器
│   └── utils.py           # 工具函数
├── mail/                  # 邮箱处理
│   ├── handler.py         # 邮箱处理器接口
│   ├── imap.py            # IMAP邮箱支持
│   └── temp.py            # 临时邮箱支持
├── generator/             # 账号生成
│   ├── account.py         # 账号信息生成
│   └── names.py           # 姓名生成器
├── logger/                # 日志系统
│   └── logger.py          # 日志管理
├── config/                # 配置管理
│   └── config.py          # 配置文件
├── turnstilePatch/        # Turnstile绕过扩展
├── names-dataset.txt      # 姓名数据集
├── .env.example           # 配置文件示例
├── main.py                # 主程序入口
└── README.md              # 项目说明
```

## 架构设计

### 二层自动化架构

1. **Action层**：提供细粒度的浏览器基础操作
   - `input.py`：输入框相关操作（输入文本、清空、获取值等）
   - `button.py`：按钮相关操作（点击、等待可用、获取文本等）
   - `navigate.py`：页面导航操作（跳转、刷新、滚动、截图等）
   - `turnstile.py`：Turnstile验证处理

2. **Flow层**：组合Action操作实现完整的业务流程
   - `cursor.py`：Cursor账号注册完整流程
   - `augment.py`：Augment账号登录完整流程

### 模块化设计

- **浏览器管理**：统一的浏览器初始化、配置和生命周期管理
- **邮箱处理**：支持IMAP、POP3和临时邮箱的统一接口
- **账号生成**：随机生成邮箱、密码、姓名等账号信息
- **配置管理**：统一的配置文件和环境变量管理
- **日志系统**：完整的日志记录和管理功能

## 安装和配置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

复制配置文件模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置必要的参数：

```env
# 邮箱域名（必填）
DOMAIN=your-domain.com

# 临时邮箱配置或IMAP配置
TEMP_MAIL=your-temp-mail-username
TEMP_MAIL_EPIN=your-temp-mail-pin
TEMP_MAIL_EXT=@your-temp-mail-domain.com

# 或者使用IMAP（将TEMP_MAIL设置为"null"）
# TEMP_MAIL=null
# IMAP_SERVER=imap.gmail.com
# IMAP_PORT=993
# IMAP_USER=<EMAIL>
# IMAP_PASS=your-email-password

# 浏览器配置
BROWSER_HEADLESS=true
FLOW_TYPE=cursor
```

### 3. 安装Chrome浏览器

确保系统已安装Chrome浏览器，程序会自动检测浏览器路径。

## 使用方法

### 命令行使用

1. **运行Cursor注册流程**：
```bash
python main.py --flow cursor
```

2. **运行Augment登录流程**：
```bash
python main.py --flow augment --email <EMAIL> --password yourpassword
```

3. **指定配置文件**：
```bash
python main.py --config config.json --env .env --flow cursor
```

4. **调试模式**：
```bash
python main.py --flow cursor --log-level DEBUG
```

### 编程接口使用

```python
from auto_register import AutoRegisterApp

# 创建应用实例
app = AutoRegisterApp()

# 初始化
app.initialize()

# 运行Cursor注册
success = app.run_cursor_registration()

# 运行Augment登录
account_info = {'email': '<EMAIL>', 'password': 'password'}
success = app.run_augment_login(account_info)

# 清理资源
app.cleanup()
```

## 配置说明

### 邮箱配置

支持两种邮箱配置方式：

1. **临时邮箱**（如tempmail.plus）：
```env
TEMP_MAIL=username
TEMP_MAIL_EPIN=pin_code
TEMP_MAIL_EXT=@domain.com
```

2. **IMAP邮箱**：
```env
TEMP_MAIL=null
IMAP_SERVER=imap.gmail.com
IMAP_PORT=993
IMAP_USER=<EMAIL>
IMAP_PASS=password
IMAP_PROTOCOL=IMAP  # 或 POP3
```

### 浏览器配置

```env
BROWSER_HEADLESS=true          # 无头模式
BROWSER_PROXY=http://proxy:port # 代理设置
BROWSER_PATH=/path/to/chrome   # 自定义浏览器路径
```

### 应用配置

```env
FLOW_TYPE=cursor              # 默认流程类型
SCREENSHOT_ENABLED=true       # 是否启用截图
LOG_LEVEL=INFO               # 日志级别
MAX_RETRIES=3                # 最大重试次数
RETRY_INTERVAL=60            # 重试间隔（秒）
```

## 功能特性

### 1. Turnstile验证处理
- 自动检测Turnstile验证挑战
- 支持扩展绕过（需要安装turnstilePatch扩展）
- 智能等待验证完成

### 2. 邮箱验证码获取
- 支持多种邮箱协议（IMAP、POP3、临时邮箱）
- 自动提取验证码
- 重试机制和错误处理

### 3. 账号信息生成
- 随机生成邮箱地址
- 强密码生成
- 真实姓名数据集支持

### 4. 截图功能
- 可配置的截图保存
- 自动截图时间戳
- 调试和问题排查支持

### 5. 日志系统
- 分级日志记录
- 文件和控制台输出
- 彩色日志支持

## 扩展开发

### 添加新的Action操作

在 `browser/automation/action/` 目录下创建新的操作模块：

```python
from ...utils import wait_for_element

class CustomAction:
    def __init__(self, tab):
        self.tab = tab
    
    def custom_operation(self):
        # 实现自定义操作
        pass
```

### 添加新的Flow流程

在 `browser/automation/flow/` 目录下创建新的流程模块：

```python
from ..action.input import InputAction
from ..action.button import ButtonAction

class CustomFlow:
    def __init__(self, tab, config=None):
        self.tab = tab
        self.config = config
        self.input_action = InputAction(tab)
        self.button_action = ButtonAction(tab)
    
    def execute_flow(self, params):
        # 实现完整的业务流程
        pass
```

### 添加新的邮箱处理器

继承 `EmailHandler` 基类：

```python
from .handler import EmailHandler

class CustomEmailHandler(EmailHandler):
    def get_verification_code(self, max_retries=5, retry_interval=60):
        # 实现验证码获取逻辑
        pass
    
    def get_latest_email(self):
        # 实现获取最新邮件逻辑
        pass
```

## 注意事项

1. **网络环境**：建议使用稳定的网络连接，海外节点效果更佳
2. **浏览器版本**：确保Chrome浏览器版本与DrissionPage兼容
3. **验证码时效**：邮箱验证码通常有时效限制，注意及时处理
4. **频率限制**：避免过于频繁的注册操作，以免触发反爬机制
5. **配置安全**：妥善保管邮箱密码等敏感配置信息

## 故障排除

### 常见问题

1. **浏览器启动失败**：
   - 检查Chrome是否正确安装
   - 确认浏览器路径配置
   - 尝试关闭无头模式进行调试

2. **邮箱验证码获取失败**：
   - 检查邮箱配置是否正确
   - 确认网络连接正常
   - 查看邮箱是否有新邮件

3. **Turnstile验证失败**：
   - 确认扩展是否正确加载
   - 尝试手动处理验证
   - 检查网络代理设置

### 调试模式

启用调试模式获取详细日志：

```bash
python main.py --flow cursor --log-level DEBUG
```

查看日志文件：
```bash
tail -f logs/$(date +%Y-%m-%d).log
```

## 许可证

本项目基于原项目许可证开源，请遵守相关使用条款。

## 贡献

欢迎提交Issue和Pull Request来改进项目。
