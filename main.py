"""
自动注册主程序
统一的程序入口，支持多种注册流程
"""
import sys
import argparse
from typing import Optional

# 导入各个模块
from config.config import init_config
from logger.logger import setup_global_logger, get_logger
from automation import Cursor, Augment


class AutoRegisterApp:
    """自动注册应用程序主类"""

    def __init__(self):
        """初始化应用程序"""
        self.config = None
        self.logger = None
        self.automation_classes = {
            'cursor': Cursor,
            'augment': Augment
        }

    def initialize(self, config_file: str = None, env_file: str = None,
                  log_level: str = "INFO"):
        """
        初始化应用程序组件

        Args:
            config_file (str): 配置文件路径
            env_file (str): 环境变量文件路径
            log_level (str): 日志级别
        """
        try:
            # 初始化配置
            self.config = init_config(config_file, env_file)

            # 初始化日志
            app_config = self.config.get_app_config()
            actual_log_level = app_config.get('log_level', log_level)
            self.logger = setup_global_logger(level=actual_log_level)

            self.logger.info("=== 自动注册程序启动 ===")

            # 验证配置
            if not self.config.validate_config():
                raise ValueError("配置验证失败，请检查配置文件")

            # 打印配置摘要
            self.config.print_config_summary()

            self.logger.info("应用程序初始化完成")

        except Exception as e:
            if self.logger:
                self.logger.error(f"应用程序初始化失败: {e}")
            else:
                print(f"应用程序初始化失败: {e}")
            raise

    def _get_automation_class(self, flow_type: str):
        """
        根据流程类型获取对应的自动化类

        Args:
            flow_type (str): 流程类型

        Returns:
            class: 自动化类
        """
        automation_class = self.automation_classes.get(flow_type.lower())
        if not automation_class:
            raise ValueError(f"不支持的流程类型: {flow_type}")
        return automation_class

    def run_flow(self, flow_type: str = None, account_info: dict = None) -> bool:
        """
        运行指定的流程

        Args:
            flow_type (str): 流程类型 ('cursor' 或 'augment')
            account_info (dict): 账号信息

        Returns:
            bool: 流程是否成功
        """
        try:
            # 确定流程类型
            if not flow_type:
                app_config = self.config.get_app_config()
                flow_type = app_config.get('flow_type', 'cursor')

            flow_type = flow_type.lower()
            self.logger.info(f"开始执行 {flow_type} 流程")

            # 获取对应的自动化类
            automation_class = self._get_automation_class(flow_type)

            # 创建自动化实例并运行
            automation_instance = automation_class(self.config)
            success = automation_instance.run(account_info)

            # 清理资源
            automation_instance.cleanup()

            return success

        except Exception as e:
            self.logger.error(f"运行流程失败: {e}")
            return False

    def cleanup(self):
        """清理资源"""
        if self.logger:
            self.logger.info("=== 自动注册程序结束 ===")


def create_argument_parser() -> argparse.ArgumentParser:
    """
    创建命令行参数解析器
    
    Returns:
        argparse.ArgumentParser: 参数解析器
    """
    parser = argparse.ArgumentParser(
        description="自动注册程序",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python main.py --flow cursor                    # 运行Cursor注册流程
  python main.py --flow augment                   # 运行Augment注册流程
  python main.py --config config.json --env .env  # 指定配置文件
        """
    )
    
    parser.add_argument(
        '--flow', 
        choices=['cursor', 'augment'],
        help='指定运行的流程类型'
    )
    
    parser.add_argument(
        '--email',
        help='指定邮箱地址（可选，不指定则自动生成）'
    )

    parser.add_argument(
        '--password',
        help='指定密码（可选，不指定则自动生成）'
    )
    
    parser.add_argument(
        '--config',
        help='配置文件路径'
    )
    
    parser.add_argument(
        '--env',
        help='环境变量文件路径'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='日志级别'
    )
    
    parser.add_argument(
        '--headless',
        action='store_true',
        help='使用无头模式运行浏览器'
    )
    
    return parser


def main():
    """主函数"""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    app = AutoRegisterApp()
    
    try:
        # 初始化应用程序
        app.initialize(
            config_file=args.config,
            env_file=args.env,
            log_level=args.log_level
        )
        
        # 准备账号信息
        account_info = None
        if args.email and args.password:
            account_info = {
                'email': args.email,
                'password': args.password
            }
        
        # 运行流程
        success = app.run_flow(args.flow, account_info)
        
        # 退出程序
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"程序运行失败: {e}")
        sys.exit(1)
    finally:
        app.cleanup()


if __name__ == "__main__":
    main()
