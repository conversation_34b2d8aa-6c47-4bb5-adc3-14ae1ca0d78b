"""
自动注册主程序
统一的程序入口，支持多种注册流程
"""
import sys
import argparse
import time
from typing import Optional

# 导入各个模块
from config.config import get_config, init_config
from logger.logger import setup_global_logger, get_logger
from browser.manager import BrowserManager
from mail.handler import EmailHandlerFactory
from generator.account import AccountGenerator
from browser.flow.cursor import CursorRegistrationFlow
from browser.flow.augment import AugmentRegistrationFlow


class AutoRegisterApp:
    """自动注册应用程序主类"""
    
    def __init__(self):
        """初始化应用程序"""
        self.config = None
        self.logger = None
        self.browser_manager = None
        self.email_handler = None
        self.account_generator = None

    def initialize(self, config_file: str = None, env_file: str = None, 
                  log_level: str = "INFO"):
        """
        初始化应用程序组件
        
        Args:
            config_file (str): 配置文件路径
            env_file (str): 环境变量文件路径
            log_level (str): 日志级别
        """
        try:
            # 初始化配置
            self.config = init_config(config_file, env_file)
            
            # 初始化日志
            app_config = self.config.get_app_config()
            actual_log_level = app_config.get('log_level', log_level)
            self.logger = setup_global_logger(level=actual_log_level)
            
            self.logger.info("=== 自动注册程序启动 ===")
            
            # 验证配置
            if not self.config.validate_config():
                raise ValueError("配置验证失败，请检查配置文件")
            
            # 打印配置摘要
            self.config.print_config_summary()
            
            # 初始化浏览器管理器
            self.browser_manager = BrowserManager()
            
            # 初始化邮箱处理器（默认为cursor类型）
            self.email_handler = EmailHandlerFactory.create_from_config(self.config, 'cursor')
            
            # 初始化账号生成器
            domain = self.config.get_domain()
            self.account_generator = AccountGenerator(domain=domain, config=self.config)
            
            self.logger.info("应用程序初始化完成")
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"应用程序初始化失败: {e}")
            else:
                print(f"应用程序初始化失败: {e}")
            raise

    def run_cursor_registration(self, account_info: dict = None) -> bool:
        """
        运行Cursor注册流程
        
        Args:
            account_info (dict): 账号信息，如果为None则自动生成
            
        Returns:
            bool: 注册是否成功
        """
        try:
            self.logger.info("开始Cursor注册流程")
            
            # 生成或使用提供的账号信息
            if not account_info:
                account_info = self.account_generator.generate_account_info()
            
            # 验证账号信息
            if not self.account_generator.validate_account_info(account_info):
                self.logger.error("账号信息验证失败")
                return False
            
            # 初始化浏览器
            browser_config = self.config.get_browser_config()
            browser = self.browser_manager.init_browser(
                headless=browser_config['headless']
            )
            
            if not browser:
                self.logger.error("浏览器初始化失败")
                return False
            
            try:
                # 获取标签页
                tab = browser.latest_tab
                
                # 创建注册流程
                registration_flow = CursorRegistrationFlow(tab, self.config)
                
                # 执行注册
                success = registration_flow.register_account(account_info)
                
                if success:
                    self.logger.info("Cursor注册成功")
                    
                    # 获取会话令牌
                    token = registration_flow.get_session_token()
                    if token:
                        self.logger.info("成功获取会话令牌")
                        # 这里可以保存令牌或进行其他处理
                    
                    return True
                else:
                    self.logger.error("Cursor注册失败")
                    return False
                    
            finally:
                # 清理浏览器资源
                self.browser_manager.quit()
                
        except Exception as e:
            self.logger.error(f"Cursor注册流程异常: {e}")
            return False

    def run_augment_registration(self, account_info: dict = None) -> bool:
        """
        运行Augment注册流程

        Args:
            account_info (dict): 账号信息，如果为None则自动生成

        Returns:
            bool: 注册是否成功
        """
        try:
            self.logger.info("开始Augment注册流程")

            # 生成或使用提供的账号信息
            if not account_info:
                account_info = self.account_generator.generate_account_info()

            # 验证账号信息
            if not self.account_generator.validate_account_info(account_info):
                self.logger.error("账号信息验证失败")
                return False
            
            # 初始化浏览器
            browser_config = self.config.get_browser_config()
            browser = self.browser_manager.init_browser(
                headless=browser_config['headless']
            )
            
            if not browser:
                self.logger.error("浏览器初始化失败")
                return False
            
            try:
                # 获取标签页
                tab = browser.latest_tab

                # 创建Augment邮箱处理器
                augment_email_handler = EmailHandlerFactory.create_from_config(self.config, 'augment')

                # 创建注册流程
                registration_flow = AugmentRegistrationFlow(tab, self.config, augment_email_handler)

                # 执行注册
                success = registration_flow.register_account(account_info)

                if success:
                    self.logger.info("Augment注册成功")

                    # 获取注册结果
                    result_info = registration_flow.get_registration_result()
                    if result_info:
                        self.logger.info(f"注册结果: {result_info}")

                    return True
                else:
                    self.logger.error("Augment注册失败")
                    return False
                    
            finally:
                # 清理浏览器资源
                self.browser_manager.quit()
                
        except Exception as e:
            self.logger.error(f"Augment注册流程异常: {e}")
            return False

    def run_flow(self, flow_type: str = None, account_info: dict = None) -> bool:
        """
        运行指定的流程
        
        Args:
            flow_type (str): 流程类型 ('cursor' 或 'augment')
            account_info (dict): 账号信息
            
        Returns:
            bool: 流程是否成功
        """
        # 确定流程类型
        if not flow_type:
            app_config = self.config.get_app_config()
            flow_type = app_config.get('flow_type', 'cursor')
        
        flow_type = flow_type.lower()
        
        if flow_type == 'cursor':
            return self.run_cursor_registration(account_info)
        elif flow_type == 'augment':
            return self.run_augment_registration(account_info)
        else:
            self.logger.error(f"不支持的流程类型: {flow_type}")
            return False

    def cleanup(self):
        """清理资源"""
        if self.browser_manager:
            self.browser_manager.quit()
        
        if self.logger:
            self.logger.info("=== 自动注册程序结束 ===")


def create_argument_parser() -> argparse.ArgumentParser:
    """
    创建命令行参数解析器
    
    Returns:
        argparse.ArgumentParser: 参数解析器
    """
    parser = argparse.ArgumentParser(
        description="自动注册程序",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python main.py --flow cursor                    # 运行Cursor注册流程
  python main.py --flow augment                   # 运行Augment注册流程
  python main.py --config config.json --env .env  # 指定配置文件
        """
    )
    
    parser.add_argument(
        '--flow', 
        choices=['cursor', 'augment'],
        help='指定运行的流程类型'
    )
    
    parser.add_argument(
        '--email',
        help='指定邮箱地址（可选，不指定则自动生成）'
    )

    parser.add_argument(
        '--password',
        help='指定密码（可选，不指定则自动生成）'
    )
    
    parser.add_argument(
        '--config',
        help='配置文件路径'
    )
    
    parser.add_argument(
        '--env',
        help='环境变量文件路径'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='日志级别'
    )
    
    parser.add_argument(
        '--headless',
        action='store_true',
        help='使用无头模式运行浏览器'
    )
    
    return parser


def main():
    """主函数"""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    app = AutoRegisterApp()
    
    try:
        # 初始化应用程序
        app.initialize(
            config_file=args.config,
            env_file=args.env,
            log_level=args.log_level
        )
        
        # 准备账号信息
        account_info = None
        if args.email and args.password:
            account_info = {
                'email': args.email,
                'password': args.password
            }
        
        # 运行流程
        success = app.run_flow(args.flow, account_info)
        
        # 退出程序
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"程序运行失败: {e}")
        sys.exit(1)
    finally:
        app.cleanup()


if __name__ == "__main__":
    main()
