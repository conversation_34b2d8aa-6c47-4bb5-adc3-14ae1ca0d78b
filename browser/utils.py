"""
浏览器工具函数
提供浏览器操作的通用工具函数
"""
import time
from typing import Optional, Union
from logger.logger import get_logger

# 获取全局日志器
logger = get_logger("BrowserUtils")


def wait_for_element(tab, selector: str, timeout: int = 10, interval: float = 0.5):
    """
    等待元素出现

    Args:
        tab: 浏览器标签页
        selector (str): 元素选择器（支持DrissionPage语法）
        timeout (int): 超时时间（秒）
        interval (float): 检查间隔（秒）

    Returns:
        element: 找到的元素，超时返回None
    """
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            # 使用DrissionPage的ele方法，支持@attribute=value语法
            element = tab.ele(selector, timeout=1)
            if element:
                return element
        except Exception:
            pass
        time.sleep(interval)

    logger.warning(f"等待元素超时: {selector}")
    return None


def wait_for_page_load(tab, timeout: int = 30):
    """
    等待页面加载完成
    
    Args:
        tab: 浏览器标签页
        timeout (int): 超时时间（秒）
        
    Returns:
        bool: 是否加载完成
    """
    try:
        tab.wait.load_start(timeout)
        return True
    except Exception as e:
        logger.warning(f"页面加载超时: {e}")
        return False


def safe_click(element, max_retries: int = 3, delay: float = 0.5):
    """
    安全点击元素（带重试机制）
    
    Args:
        element: 要点击的元素
        max_retries (int): 最大重试次数
        delay (float): 重试间隔（秒）
        
    Returns:
        bool: 是否点击成功
    """
    for attempt in range(max_retries):
        try:
            if element and element.click():
                return True
        except Exception as e:
            logger.warning(f"点击失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(delay)
    
    return False


def safe_input(element, text: str, clear_first: bool = True, max_retries: int = 3):
    """
    安全输入文本（带重试机制）
    
    Args:
        element: 输入元素
        text (str): 要输入的文本
        clear_first (bool): 是否先清空
        max_retries (int): 最大重试次数
        
    Returns:
        bool: 是否输入成功
    """
    for attempt in range(max_retries):
        try:
            if element:
                if clear_first:
                    element.clear()
                element.input(text)
                return True
        except Exception as e:
            logger.warning(f"输入失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(0.5)
    
    return False


def get_element_text(tab, selector: str, default: str = "") -> str:
    """
    获取元素文本内容
    
    Args:
        tab: 浏览器标签页
        selector (str): 元素选择器
        default (str): 默认值
        
    Returns:
        str: 元素文本内容
    """
    try:
        element = tab.ele(selector)
        if element:
            return element.text or default
    except Exception as e:
        logger.warning(f"获取元素文本失败: {e}")

    return default


def scroll_to_element(tab, element):
    """
    滚动到指定元素
    
    Args:
        tab: 浏览器标签页
        element: 目标元素
        
    Returns:
        bool: 是否滚动成功
    """
    try:
        if element:
            element.scroll.to_see()
            return True
    except Exception as e:
        logger.warning(f"滚动到元素失败: {e}")

    return False


def take_screenshot(tab, filename: str = None) -> Optional[str]:
    """
    截取页面截图
    
    Args:
        tab: 浏览器标签页
        filename (str, optional): 保存文件名，None时自动生成
        
    Returns:
        str: 截图文件路径，失败返回None
    """
    try:
        if filename is None:
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}.png"
        
        # 确保截图目录存在
        import os
        screenshot_dir = "screen"
        if not os.path.exists(screenshot_dir):
            os.makedirs(screenshot_dir)
        
        filepath = os.path.join(screenshot_dir, filename)
        tab.get_screenshot(path=filepath)
        return filepath
    except Exception as e:
        logger.error(f"截图失败: {e}")
        return None


def switch_to_latest_tab(browser):
    """
    切换到最新的标签页
    
    Args:
        browser: 浏览器实例
        
    Returns:
        tab: 最新的标签页
    """
    try:
        return browser.latest_tab
    except Exception as e:
        logger.warning(f"切换标签页失败: {e}")
        return None


def close_extra_tabs(browser, keep_count: int = 1):
    """
    关闭多余的标签页，只保留指定数量
    
    Args:
        browser: 浏览器实例
        keep_count (int): 保留的标签页数量
        
    Returns:
        bool: 是否操作成功
    """
    try:
        tabs = browser.tabs
        if len(tabs) > keep_count:
            for tab in tabs[keep_count:]:
                tab.close()
        return True
    except Exception as e:
        logger.warning(f"关闭标签页失败: {e}")
        return False
