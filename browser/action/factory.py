"""
Action 工厂模块
提供简化的 Action 创建方法
"""
from .input import InputAction
from .button import ButtonAction
from .navigate import NavigateAction
from .turnstile import TurnstileAction


class ActionFactory:
    """Action 工厂类"""
    
    def __init__(self, tab):
        """
        初始化 Action 工厂
        
        Args:
            tab: 浏览器标签页
        """
        self.tab = tab
    
    def input(self, selector: str) -> InputAction:
        """
        创建输入框操作实例
        
        Args:
            selector (str): 输入框选择器
            
        Returns:
            InputAction: 输入框操作实例
        """
        return InputAction(self.tab, selector)
    
    def button(self, selector: str) -> ButtonAction:
        """
        创建按钮操作实例
        
        Args:
            selector (str): 按钮选择器
            
        Returns:
            ButtonAction: 按钮操作实例
        """
        return ButtonAction(self.tab, selector)
    
    def navigate(self) -> NavigateAction:
        """
        创建导航操作实例
        
        Returns:
            NavigateAction: 导航操作实例
        """
        return NavigateAction(self.tab)
    
    def turnstile(self) -> TurnstileAction:
        """
        创建 Turnstile 操作实例
        
        Returns:
            TurnstileAction: Turnstile 操作实例
        """
        return TurnstileAction(self.tab)


# 便捷函数
def create_actions(tab):
    """
    创建 Action 工厂实例
    
    Args:
        tab: 浏览器标签页
        
    Returns:
        ActionFactory: Action 工厂实例
    """
    return ActionFactory(tab)
