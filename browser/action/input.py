"""
输入框操作模块
提供各种输入框相关的基础操作
"""
import time
from typing import Optional, Union
from logger.logger import get_logger
from ...utils import wait_for_element, safe_input


class InputAction:
    """输入框操作类"""

    def __init__(self, tab):
        """
        初始化输入框操作

        Args:
            tab: 浏览器标签页
        """
        self.tab = tab
        self.logger = get_logger("InputAction")

    def input_text(self, selector: str, text: str, clear_first: bool = True,
                   wait_timeout: int = 10) -> bool:
        """
        在指定输入框中输入文本

        Args:
            selector (str): 输入框选择器（支持DrissionPage语法）
            text (str): 要输入的文本
            clear_first (bool): 是否先清空输入框
            wait_timeout (int): 等待元素出现的超时时间

        Returns:
            bool: 是否输入成功
        """
        try:
            # 使用DrissionPage的方式：先点击元素聚焦，然后输入
            element = wait_for_element(self.tab, selector, wait_timeout)
            if not element:
                self.logger.error(f"未找到输入框: {selector}")
                return False

            # 使用DrissionPage的actions.click().input()方式
            if hasattr(self.tab, 'actions'):
                self.tab.actions.click(selector).input(text)
                self.logger.debug(f"成功输入文本到 {selector}: {text}")
                return True
            else:
                # 备用方法
                return safe_input(element, text, clear_first)
        except Exception as e:
            self.logger.error(f"输入文本失败: {e}")
            return False

    def input_email(self, selector: str, email: str, wait_timeout: int = 10) -> bool:
        """
        输入邮箱地址
        
        Args:
            selector (str): 邮箱输入框选择器
            email (str): 邮箱地址
            wait_timeout (int): 等待超时时间
            
        Returns:
            bool: 是否输入成功
        """
        self.logger.info(f"输入邮箱: {email}")
        return self.input_text(selector, email, wait_timeout=wait_timeout)

    def input_password(self, selector: str, password: str, wait_timeout: int = 10) -> bool:
        """
        输入密码
        
        Args:
            selector (str): 密码输入框选择器
            password (str): 密码
            wait_timeout (int): 等待超时时间
            
        Returns:
            bool: 是否输入成功
        """
        self.logger.info("输入密码")
        return self.input_text(selector, password, wait_timeout=wait_timeout)

    def input_name(self, selector: str, name: str, wait_timeout: int = 10) -> bool:
        """
        输入姓名
        
        Args:
            selector (str): 姓名输入框选择器
            name (str): 姓名
            wait_timeout (int): 等待超时时间
            
        Returns:
            bool: 是否输入成功
        """
        self.logger.info(f"输入姓名: {name}")
        return self.input_text(selector, name, wait_timeout=wait_timeout)

    def input_verification_code(self, selector: str, code: str, wait_timeout: int = 10) -> bool:
        """
        输入验证码
        
        Args:
            selector (str): 验证码输入框选择器
            code (str): 验证码
            wait_timeout (int): 等待超时时间
            
        Returns:
            bool: 是否输入成功
        """
        self.logger.info(f"输入验证码: {code}")
        return self.input_text(selector, code, wait_timeout=wait_timeout)

    def clear_input(self, selector: str, wait_timeout: int = 10) -> bool:
        """
        清空输入框
        
        Args:
            selector (str): 输入框选择器
            wait_timeout (int): 等待超时时间
            
        Returns:
            bool: 是否清空成功
        """
        try:
            element = wait_for_element(self.tab, selector, wait_timeout)
            if not element:
                self.logger.error(f"未找到输入框: {selector}")
                return False

            element.clear()
            self.logger.info(f"已清空输入框: {selector}")
            return True
        except Exception as e:
            self.logger.error(f"清空输入框失败: {e}")
            return False

    def get_input_value(self, selector: str, wait_timeout: int = 10) -> Optional[str]:
        """
        获取输入框的值
        
        Args:
            selector (str): 输入框选择器
            wait_timeout (int): 等待超时时间
            
        Returns:
            str: 输入框的值，获取失败返回None
        """
        try:
            element = wait_for_element(self.tab, selector, wait_timeout)
            if not element:
                self.logger.error(f"未找到输入框: {selector}")
                return None

            return element.value
        except Exception as e:
            self.logger.error(f"获取输入框值失败: {e}")
            return None

    def is_input_empty(self, selector: str, wait_timeout: int = 10) -> bool:
        """
        检查输入框是否为空
        
        Args:
            selector (str): 输入框选择器
            wait_timeout (int): 等待超时时间
            
        Returns:
            bool: 输入框是否为空
        """
        value = self.get_input_value(selector, wait_timeout)
        return not value or value.strip() == ""

    def focus_input(self, selector: str, wait_timeout: int = 10) -> bool:
        """
        聚焦到输入框
        
        Args:
            selector (str): 输入框选择器
            wait_timeout (int): 等待超时时间
            
        Returns:
            bool: 是否聚焦成功
        """
        try:
            element = wait_for_element(self.tab, selector, wait_timeout)
            if not element:
                self.logger.error(f"未找到输入框: {selector}")
                return False

            element.click()
            time.sleep(0.2)  # 短暂等待聚焦生效
            return True
        except Exception as e:
            self.logger.error(f"聚焦输入框失败: {e}")
            return False

    def input_with_delay(self, selector: str, text: str, delay: float = 0.1, 
                        wait_timeout: int = 10) -> bool:
        """
        逐字符输入文本（模拟人工输入）
        
        Args:
            selector (str): 输入框选择器
            text (str): 要输入的文本
            delay (float): 每个字符间的延迟时间
            wait_timeout (int): 等待超时时间
            
        Returns:
            bool: 是否输入成功
        """
        try:
            element = wait_for_element(self.tab, selector, wait_timeout)
            if not element:
                self.logger.error(f"未找到输入框: {selector}")
                return False

            element.clear()
            for char in text:
                element.input(char)
                time.sleep(delay)

            self.logger.info(f"逐字符输入完成: {text}")
            return True
        except Exception as e:
            self.logger.error(f"逐字符输入失败: {e}")
            return False
