"""
输入框操作模块
提供各种输入框相关的基础操作
"""
import time
from typing import Optional, Union
from logger.logger import get_logger
from ..utils import wait_for_element, safe_input


class InputAction:
    """输入框操作类 - 支持链式调用"""

    def __init__(self, tab, selector: str):
        """
        初始化输入框操作

        Args:
            tab: 浏览器标签页
            selector (str): 输入框选择器
        """
        self.tab = tab
        self.selector = selector
        self.logger = get_logger("InputAction")
        self.element = None

    def _get_element(self, wait_timeout: int = 10):
        """获取元素（内部方法）"""
        if not self.element:
            self.element = wait_for_element(self.tab, self.selector, wait_timeout)
        return self.element

    def focus(self, wait_timeout: int = 10):
        """
        聚焦到输入框（支持链式调用）

        Args:
            wait_timeout (int): 等待超时时间

        Returns:
            InputAction: 返回自身支持链式调用
        """
        try:
            element = self._get_element(wait_timeout)
            if element:
                element.click()
                self.logger.debug(f"聚焦到输入框: {self.selector}")
            else:
                self.logger.error(f"未找到输入框: {self.selector}")
        except Exception as e:
            self.logger.error(f"聚焦输入框失败: {e}")
        return self

    def clear(self, wait_timeout: int = 10):
        """
        清空输入框（支持链式调用）

        Args:
            wait_timeout (int): 等待超时时间

        Returns:
            InputAction: 返回自身支持链式调用
        """
        try:
            element = self._get_element(wait_timeout)
            if element:
                element.clear()
                self.logger.debug(f"清空输入框: {self.selector}")
            else:
                self.logger.error(f"未找到输入框: {self.selector}")
        except Exception as e:
            self.logger.error(f"清空输入框失败: {e}")
        return self

    def input(self, text: str, wait_timeout: int = 10):
        """
        输入文本（支持链式调用）

        Args:
            text (str): 要输入的文本
            wait_timeout (int): 等待超时时间

        Returns:
            InputAction: 返回自身支持链式调用
        """
        try:
            element = self._get_element(wait_timeout)
            if element:
                # 使用DrissionPage的方式输入
                if hasattr(self.tab, 'actions'):
                    self.tab.actions.click(self.selector).input(text)
                else:
                    element.input(text)
                self.logger.debug(f"输入文本到 {self.selector}: {text}")
            else:
                self.logger.error(f"未找到输入框: {self.selector}")
        except Exception as e:
            self.logger.error(f"输入文本失败: {e}")
        return self

    def value(self, wait_timeout: int = 10) -> Optional[str]:
        """
        获取输入框的值

        Args:
            wait_timeout (int): 等待超时时间

        Returns:
            str: 输入框的值，获取失败返回None
        """
        try:
            element = self._get_element(wait_timeout)
            if element:
                return element.value
            return None
        except Exception as e:
            self.logger.error(f"获取输入框值失败: {e}")
            return None

    def is_empty(self, wait_timeout: int = 10) -> bool:
        """
        检查输入框是否为空

        Args:
            wait_timeout (int): 等待超时时间

        Returns:
            bool: 输入框是否为空
        """
        value = self.value(wait_timeout)
        return not value or value.strip() == ""

    # 向后兼容的方法
    def input_text(self, selector: str, text: str, clear_first: bool = True,
                   wait_timeout: int = 10) -> bool:
        """向后兼容的输入文本方法"""
        try:
            element = wait_for_element(self.tab, selector, wait_timeout)
            if not element:
                return False
            if clear_first:
                element.clear()
            element.input(text)
            return True
        except Exception:
            return False

    def input_email(self, selector: str, email: str, wait_timeout: int = 10) -> bool:
        """向后兼容的输入邮箱方法"""
        return self.input_text(selector, email, wait_timeout=wait_timeout)

    def input_password(self, selector: str, password: str, wait_timeout: int = 10) -> bool:
        """向后兼容的输入密码方法"""
        return self.input_text(selector, password, wait_timeout=wait_timeout)

    def input_name(self, selector: str, name: str, wait_timeout: int = 10) -> bool:
        """向后兼容的输入姓名方法"""
        return self.input_text(selector, name, wait_timeout=wait_timeout)

    def input_verification_code(self, selector: str, code: str, wait_timeout: int = 10) -> bool:
        """向后兼容的输入验证码方法"""
        return self.input_text(selector, code, wait_timeout=wait_timeout)

    def clear_input(self, selector: str, wait_timeout: int = 10) -> bool:
        """
        清空输入框
        
        Args:
            selector (str): 输入框选择器
            wait_timeout (int): 等待超时时间
            
        Returns:
            bool: 是否清空成功
        """
        try:
            element = wait_for_element(self.tab, selector, wait_timeout)
            if not element:
                self.logger.error(f"未找到输入框: {selector}")
                return False

            element.clear()
            self.logger.info(f"已清空输入框: {selector}")
            return True
        except Exception as e:
            self.logger.error(f"清空输入框失败: {e}")
            return False

    def get_input_value(self, selector: str, wait_timeout: int = 10) -> Optional[str]:
        """
        获取输入框的值
        
        Args:
            selector (str): 输入框选择器
            wait_timeout (int): 等待超时时间
            
        Returns:
            str: 输入框的值，获取失败返回None
        """
        try:
            element = wait_for_element(self.tab, selector, wait_timeout)
            if not element:
                self.logger.error(f"未找到输入框: {selector}")
                return None

            return element.value
        except Exception as e:
            self.logger.error(f"获取输入框值失败: {e}")
            return None

    def is_input_empty(self, selector: str, wait_timeout: int = 10) -> bool:
        """
        检查输入框是否为空
        
        Args:
            selector (str): 输入框选择器
            wait_timeout (int): 等待超时时间
            
        Returns:
            bool: 输入框是否为空
        """
        value = self.get_input_value(selector, wait_timeout)
        return not value or value.strip() == ""

    def focus_input(self, selector: str, wait_timeout: int = 10) -> bool:
        """
        聚焦到输入框
        
        Args:
            selector (str): 输入框选择器
            wait_timeout (int): 等待超时时间
            
        Returns:
            bool: 是否聚焦成功
        """
        try:
            element = wait_for_element(self.tab, selector, wait_timeout)
            if not element:
                self.logger.error(f"未找到输入框: {selector}")
                return False

            element.click()
            time.sleep(0.2)  # 短暂等待聚焦生效
            return True
        except Exception as e:
            self.logger.error(f"聚焦输入框失败: {e}")
            return False

    def input_with_delay(self, selector: str, text: str, delay: float = 0.1, 
                        wait_timeout: int = 10) -> bool:
        """
        逐字符输入文本（模拟人工输入）
        
        Args:
            selector (str): 输入框选择器
            text (str): 要输入的文本
            delay (float): 每个字符间的延迟时间
            wait_timeout (int): 等待超时时间
            
        Returns:
            bool: 是否输入成功
        """
        try:
            element = wait_for_element(self.tab, selector, wait_timeout)
            if not element:
                self.logger.error(f"未找到输入框: {selector}")
                return False

            element.clear()
            for char in text:
                element.input(char)
                time.sleep(delay)

            self.logger.info(f"逐字符输入完成: {text}")
            return True
        except Exception as e:
            self.logger.error(f"逐字符输入失败: {e}")
            return False
