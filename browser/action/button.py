"""
按钮操作模块
提供各种按钮相关的基础操作
"""
import time
from typing import Optional, List
from logger.logger import get_logger
from ..utils import wait_for_element, safe_click, scroll_to_element


class ButtonAction:
    """按钮操作类 - 支持链式调用"""

    def __init__(self, tab, selector: str):
        """
        初始化按钮操作

        Args:
            tab: 浏览器标签页
            selector (str): 按钮选择器
        """
        self.tab = tab
        self.selector = selector
        self.logger = get_logger("ButtonAction")
        self.element = None

    def _get_element(self, wait_timeout: int = 10):
        """获取元素（内部方法）"""
        if not self.element:
            self.element = wait_for_element(self.tab, self.selector, wait_timeout)
        return self.element

    def click(self, wait_timeout: int = 10, scroll_first: bool = True):
        """
        点击按钮（支持链式调用）

        Args:
            wait_timeout (int): 等待元素出现的超时时间
            scroll_first (bool): 是否先滚动到按钮位置

        Returns:
            ButtonAction: 返回自身支持链式调用
        """
        try:
            element = self._get_element(wait_timeout)
            if not element:
                self.logger.error(f"未找到按钮: {self.selector}")
                return self

            # 滚动到按钮位置
            if scroll_first:
                scroll_to_element(self.tab, element)
                time.sleep(0.5)

            # 使用DrissionPage的方式点击
            if hasattr(self.tab, 'actions'):
                self.tab.actions.click(self.selector)
            else:
                element.click()

            self.logger.debug(f"点击按钮: {self.selector}")
        except Exception as e:
            self.logger.error(f"点击按钮异常: {e}")
        return self

    def double_click(self, wait_timeout: int = 10):
        """
        双击按钮（支持链式调用）

        Args:
            wait_timeout (int): 等待超时时间

        Returns:
            ButtonAction: 返回自身支持链式调用
        """
        try:
            element = self._get_element(wait_timeout)
            if element:
                element.click()
                time.sleep(0.1)
                element.click()
                self.logger.debug(f"双击按钮: {self.selector}")
            else:
                self.logger.error(f"未找到按钮: {self.selector}")
        except Exception as e:
            self.logger.error(f"双击按钮失败: {e}")
        return self

    def is_enabled(self, wait_timeout: int = 10) -> bool:
        """
        检查按钮是否可用

        Args:
            wait_timeout (int): 等待超时时间

        Returns:
            bool: 按钮是否可用
        """
        try:
            element = self._get_element(wait_timeout)
            if not element:
                return False

            # 检查disabled属性
            disabled = element.attr('disabled')
            return disabled is None or disabled == 'false'
        except Exception as e:
            self.logger.error(f"检查按钮状态失败: {e}")
            return False

    def text(self, wait_timeout: int = 10) -> Optional[str]:
        """
        获取按钮文本

        Args:
            wait_timeout (int): 等待超时时间

        Returns:
            str: 按钮文本，获取失败返回None
        """
        try:
            element = self._get_element(wait_timeout)
            if element:
                return element.text
            return None
        except Exception as e:
            self.logger.error(f"获取按钮文本失败: {e}")
            return None

    def wait_enabled(self, timeout: int = 30, check_interval: float = 0.5) -> bool:
        """
        等待按钮变为可用状态

        Args:
            timeout (int): 超时时间
            check_interval (float): 检查间隔

        Returns:
            bool: 按钮是否变为可用
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            if self.is_enabled(1):
                return True
            time.sleep(check_interval)

        self.logger.warning(f"等待按钮可用超时: {self.selector}")
        return False

    # 向后兼容的方法
    def click_button(self, selector: str, wait_timeout: int = 10,
                    scroll_first: bool = True) -> bool:
        """向后兼容的点击按钮方法"""
        try:
            element = wait_for_element(self.tab, selector, wait_timeout)
            if not element:
                return False
            if scroll_first:
                scroll_to_element(self.tab, element)
                time.sleep(0.5)
            if hasattr(self.tab, 'actions'):
                self.tab.actions.click(selector)
            else:
                element.click()
            return True
        except Exception:
            return False

    def click_button_by_text(self, text: str, wait_timeout: int = 10) -> bool:
        """向后兼容的根据文本点击按钮方法"""
        selector = f'button:contains("{text}")'
        return self.click_button(selector, wait_timeout)

    def is_button_enabled(self, selector: str, wait_timeout: int = 10) -> bool:
        """
        检查按钮是否可用
        
        Args:
            selector (str): 按钮选择器
            wait_timeout (int): 等待超时时间
            
        Returns:
            bool: 按钮是否可用
        """
        try:
            element = wait_for_element(self.tab, selector, wait_timeout)
            if not element:
                return False
            
            # 检查disabled属性
            disabled = element.attr('disabled')
            return disabled is None or disabled == 'false'
        except Exception as e:
            self.logger.error(f"检查按钮状态失败: {e}")
            return False

    def wait_for_button_enabled(self, selector: str, timeout: int = 30, 
                               check_interval: float = 0.5) -> bool:
        """
        等待按钮变为可用状态
        
        Args:
            selector (str): 按钮选择器
            timeout (int): 超时时间
            check_interval (float): 检查间隔
            
        Returns:
            bool: 按钮是否变为可用
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            if self.is_button_enabled(selector, 1):
                return True
            time.sleep(check_interval)

        self.logger.warning(f"等待按钮可用超时: {selector}")
        return False

    def get_button_text(self, selector: str, wait_timeout: int = 10) -> Optional[str]:
        """
        获取按钮文本
        
        Args:
            selector (str): 按钮选择器
            wait_timeout (int): 等待超时时间
            
        Returns:
            str: 按钮文本，获取失败返回None
        """
        try:
            element = wait_for_element(self.tab, selector, wait_timeout)
            if not element:
                return None
            
            return element.text
        except Exception as e:
            self.logger.error(f"获取按钮文本失败: {e}")
            return None

    def click_button_by_text(self, text: str, wait_timeout: int = 10) -> bool:
        """
        根据文本内容点击按钮
        
        Args:
            text (str): 按钮文本
            wait_timeout (int): 等待超时时间
            
        Returns:
            bool: 是否点击成功
        """
        selector = f'button:contains("{text}")'
        return self.click_button(selector, wait_timeout)

    def double_click_button(self, selector: str, wait_timeout: int = 10) -> bool:
        """
        双击按钮

        Args:
            selector (str): 按钮选择器
            wait_timeout (int): 等待超时时间

        Returns:
            bool: 是否双击成功
        """
        try:
            element = wait_for_element(self.tab, selector, wait_timeout)
            if not element:
                self.logger.error(f"未找到按钮: {selector}")
                return False

            element.click()
            time.sleep(0.1)
            element.click()

            self.logger.info(f"成功双击按钮: {selector}")
            return True
        except Exception as e:
            self.logger.error(f"双击按钮失败: {e}")
            return False

    def click_checkbox(self, selector: str, wait_timeout: int = 10,
                      scroll_first: bool = True) -> bool:
        """
        点击复选框

        Args:
            selector (str): 复选框选择器（支持DrissionPage语法）
            wait_timeout (int): 等待元素出现的超时时间
            scroll_first (bool): 是否先滚动到复选框位置

        Returns:
            bool: 是否点击成功
        """
        try:
            element = wait_for_element(self.tab, selector, wait_timeout)
            if not element:
                self.logger.error(f"未找到复选框: {selector}")
                return False

            # 滚动到复选框位置
            if scroll_first:
                scroll_to_element(self.tab, element)
                time.sleep(0.5)

            # 使用DrissionPage的方式点击
            if hasattr(self.tab, 'actions'):
                self.tab.actions.click(selector)
                self.logger.info(f"成功点击复选框: {selector}")
                return True
            else:
                # 备用方法
                success = safe_click(element)
                if success:
                    self.logger.info(f"成功点击复选框: {selector}")
                else:
                    self.logger.error(f"点击复选框失败: {selector}")
                return success
        except Exception as e:
            self.logger.error(f"点击复选框异常: {e}")
            return False

    def is_checkbox_checked(self, selector: str, wait_timeout: int = 10) -> bool:
        """
        检查复选框是否已选中

        Args:
            selector (str): 复选框选择器
            wait_timeout (int): 等待超时时间

        Returns:
            bool: 复选框是否已选中
        """
        try:
            element = wait_for_element(self.tab, selector, wait_timeout)
            if not element:
                self.logger.error(f"未找到复选框: {selector}")
                return False

            # 检查checked属性
            checked = element.attr('checked')
            return checked is not None and checked != 'false'
        except Exception as e:
            self.logger.error(f"检查复选框状态失败: {e}")
            return False

