"""
Turnstile验证操作模块
提供Cloudflare Turnstile验证相关的操作（完全复制原始逻辑）
"""
import time
import random
import os
from typing import Optional
from enum import Enum
from logger.logger import get_logger
from config.config import get_config


class VerificationStatus(Enum):
    """Verification status enum（从原始代码复制）"""
    PASSWORD_PAGE = "@name=password"
    CAPTCHA_PAGE = "@data-index=0"
    SUCCESS = "svg#success-i"
    ACCOUNT_SETTINGS = "Account Settings"


class TurnstileError(Exception):
    """Turnstile verification related exception"""
    pass


class TurnstileAction:
    """Turnstile验证操作类"""

    def __init__(self, tab):
        """
        初始化Turnstile验证操作

        Args:
            tab: 浏览器标签页
        """
        self.tab = tab
        self.logger = get_logger("TurnstileAction")

    def save_screenshot(self, stage: str, timestamp: bool = True) -> None:
        """
        Save screenshot for debugging

        Args:
            stage (str): Current stage name
            timestamp (bool): Whether to add timestamp to filename
        """
        try:
            enableScreenshot = os.getenv("SCREENSHOT_ENABLED", "False").lower() == "true"
            if not enableScreenshot == False:
                return
            screenshot_dir = "screen"
            if not os.path.exists(screenshot_dir):
                os.makedirs(screenshot_dir)

            # Generate filename
            if timestamp:
                filename = f"turnstile_{stage}_{int(time.time())}.png"
            else:
                filename = f"turnstile_{stage}.png"

            filepath = os.path.join(screenshot_dir, filename)

            # Save screenshot
            self.tab.get_screenshot(filepath)
            self.logger.debug(f"Screenshot saved: {filepath}")
        except Exception as e:
            self.logger.warning(f"Failed to save screenshot: {e}")

    def check_verification_success(self, containerId) -> Optional[VerificationStatus]:
        """
        Check if verification was successful（原始逻辑完全复制）

        Returns:
            VerificationStatus: The corresponding status if successful, None if failed
        """
        for status in VerificationStatus:
            if self.tab.ele(status.value):
                self.logger.info(f"验证成功: {status.name}")
                return status
        # final check success svg
        success = (self.tab.ele(f"@id={containerId}", timeout=2)
                        .child()
                        .shadow_root.ele("tag:iframe")
                        .ele("tag:body")
                        .sr("tag:svg@id=success-i"))
        return success

    def handle_turnstile_verification(self, containerId = "cf-turnstile", max_retries: int = 2, retry_interval: tuple = (1, 2)) -> bool:
        """
        Handle Turnstile verification (原始逻辑完全复制)

        Args:
            max_retries: Maximum number of retries
            retry_interval: Retry interval range (min, max)

        Returns:
            bool: Whether verification was successful

        Raises:
            TurnstileError: Exception during verification process
        """
        self.logger.info("检测Turnstile验证")
        self.save_screenshot("start")

        retry_count = 0

        try:
            while retry_count < max_retries:
                retry_count += 1
                self.logger.debug(f"重试验证 (第 {retry_count} 次)")

                try:
                    # Locate verification frame element
                    challenge_check = (
                        self.tab.ele(f"@id={containerId}", timeout=2)
                        .child()
                        .shadow_root.ele("tag:iframe")
                        .ele("tag:body")
                        .sr("tag:input")
                    )

                    if challenge_check:
                        self.logger.info("检测到Turnstile验证")
                        # Random delay before clicking verification
                        time.sleep(random.uniform(1, 3))
                        challenge_check.click()
                        time.sleep(2)

                        # Save screenshot after verification
                        self.save_screenshot("clicked")

                        # Check verification result
                        if self.check_verification_success(containerId):
                            self.logger.info("Turnstile验证通过")
                            self.save_screenshot("success")
                            return True

                except Exception as e:
                    self.logger.debug(f"Current attempt unsuccessful: {str(e)}")

                # Check if already verified
                if self.check_verification_success(containerId):
                    return True

                # Random delay before next attempt
                time.sleep(random.uniform(*retry_interval))

            # 如果没有找到Turnstile元素，可能验证已经通过或不需要验证
            # 检查是否已经在下一个状态
            if self.check_verification_success(containerId):
                self.logger.info("Turnstile验证可能已经通过或不需要验证")
                return True

            # Exceeded maximum retries
            self.logger.error(f"验证失败，已达到最大重试次数: {max_retries}")
            self.save_screenshot("failed")
            return False

        except Exception as e:
            error_msg = f"Turnstile验证异常: {str(e)}"
            self.logger.error(error_msg)
            self.save_screenshot("error")
            raise TurnstileError(error_msg)
