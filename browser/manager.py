"""
浏览器管理器
负责浏览器的初始化、配置和生命周期管理
"""
from DrissionPage import ChromiumOptions, Chromium
import sys
import os
from logger.logger import get_logger
from dotenv import load_dotenv

load_dotenv()

# 获取日志器
logger = get_logger("BrowserManager")


class BrowserManager:
    """浏览器管理器类"""
    
    def __init__(self):
        self.browser = None
        self._options = None

    def init_browser(self, user_agent=None, headless=None):
        """
        初始化浏览器
        
        Args:
            user_agent (str, optional): 自定义用户代理
            headless (bool, optional): 是否使用无头模式，None时使用环境变量配置
            
        Returns:
            Chromium: 浏览器实例
        """
        self._options = self._get_browser_options(user_agent, headless)
        self.browser = Chromium(self._options)
        return self.browser

    def _get_browser_options(self, user_agent=None, headless=None):
        """
        获取浏览器配置选项
        
        Args:
            user_agent (str, optional): 自定义用户代理
            headless (bool, optional): 是否使用无头模式
            
        Returns:
            ChromiumOptions: 浏览器配置选项
        """
        co = ChromiumOptions()
        
        # 添加扩展
        try:
            extension_path = self._get_extension_path("turnstilePatch")
            co.add_extension(extension_path)
        except FileNotFoundError as e:
            logger.warning(f"警告: {e}")

        # 设置浏览器路径
        browser_path = os.getenv("BROWSER_PATH")
        if browser_path:
            co.set_paths(browser_path=browser_path)

        # 基础配置
        co.set_pref("credentials_enable_service", False)
        co.set_argument("--hide-crash-restore-bubble")
        
        # 代理配置
        proxy = os.getenv("BROWSER_PROXY")
        if proxy:
            co.set_proxy(proxy)

        # 自动端口
        co.auto_port()
        
        # 用户代理
        if user_agent:
            co.set_user_agent(user_agent)

        # 无头模式配置
        if headless is None:
            headless = os.getenv("BROWSER_HEADLESS", "True").lower() == "true"
        co.headless(headless)

        # Mac 系统特殊处理
        if sys.platform == "darwin":
            co.set_argument("--no-sandbox")
            co.set_argument("--disable-gpu")

        return co

    def _get_extension_path(self, extension_name='turnstilePatch'):
        """
        获取浏览器扩展路径
        
        Args:
            extension_name (str): 扩展名称
            
        Returns:
            str: 扩展路径
            
        Raises:
            FileNotFoundError: 扩展不存在时抛出
        """
        root_dir = os.getcwd()
        extension_path = os.path.join(root_dir, extension_name)

        # 处理打包后的可执行文件
        if hasattr(sys, "_MEIPASS"):
            extension_path = os.path.join(sys._MEIPASS, extension_name)

        if not os.path.exists(extension_path):
            raise FileNotFoundError(f"插件不存在: {extension_path}")

        return extension_path

    def get_browser(self):
        """
        获取当前浏览器实例
        
        Returns:
            Chromium: 浏览器实例，如果未初始化则返回None
        """
        return self.browser

    def quit(self):
        """关闭浏览器"""
        if self.browser:
            try:
                self.browser.quit()
                self.browser = None
            except Exception as e:
                logger.warning(f"关闭浏览器时出现异常: {e}")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.quit()
