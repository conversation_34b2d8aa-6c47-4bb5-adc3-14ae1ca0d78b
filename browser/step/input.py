"""
输入步骤操作模块
提供通用的输入操作步骤
"""
from typing import List, Optional
from ..action.input import InputAction
from logger.logger import get_logger


class InputStep:
    """输入步骤操作类"""
    
    def __init__(self, tab):
        """
        初始化输入步骤操作
        
        Args:
            tab: 浏览器标签页
        """
        self.tab = tab
        self.logger = get_logger("InputStep")
        self.input_action = InputAction(tab)
    
    def input_email_with_selectors(self, email: str, selectors: List[str] = None, 
                                  wait_timeout: int = 10) -> bool:
        """
        使用多个选择器尝试输入邮箱
        
        Args:
            email (str): 邮箱地址
            selectors (List[str]): 选择器列表，None时使用默认选择器
            wait_timeout (int): 等待超时时间
            
        Returns:
            bool: 是否输入成功
        """
        if selectors is None:
            selectors = [
                '@name=email',
                'input[name="email"]',
                'input[type="email"]',
                '#email',
                '.email-input',
                '[placeholder*="email" i]',
                '[placeholder*="邮箱" i]'
            ]
        
        self.logger.info(f"尝试输入邮箱: {email}")
        
        for selector in selectors:
            if self.input_action.input_email(selector, email, wait_timeout=2):
                self.logger.info(f"成功使用选择器输入邮箱: {selector}")
                return True
        
        self.logger.error("所有邮箱选择器都失败")
        return False
    
    def input_password_with_selectors(self, password: str, selectors: List[str] = None,
                                     wait_timeout: int = 10) -> bool:
        """
        使用多个选择器尝试输入密码
        
        Args:
            password (str): 密码
            selectors (List[str]): 选择器列表，None时使用默认选择器
            wait_timeout (int): 等待超时时间
            
        Returns:
            bool: 是否输入成功
        """
        if selectors is None:
            selectors = [
                '@name=password',
                'input[name="password"]',
                'input[type="password"]',
                '#password',
                '.password-input',
                '[placeholder*="password" i]',
                '[placeholder*="密码" i]'
            ]
        
        self.logger.info("尝试输入密码")
        
        for selector in selectors:
            if self.input_action.input_password(selector, password, wait_timeout=2):
                self.logger.info(f"成功使用选择器输入密码: {selector}")
                return True
        
        self.logger.error("所有密码选择器都失败")
        return False
    
    def input_name_with_selectors(self, name: str, field_type: str = "first_name",
                                 selectors: List[str] = None, wait_timeout: int = 10) -> bool:
        """
        使用多个选择器尝试输入姓名
        
        Args:
            name (str): 姓名
            field_type (str): 字段类型 ("first_name", "last_name", "full_name")
            selectors (List[str]): 选择器列表，None时使用默认选择器
            wait_timeout (int): 等待超时时间
            
        Returns:
            bool: 是否输入成功
        """
        if selectors is None:
            if field_type == "first_name":
                selectors = [
                    '@name=first_name',
                    '@name=firstName',
                    'input[name="first_name"]',
                    'input[name="firstName"]',
                    '#first_name',
                    '#firstName',
                    '[placeholder*="first" i]',
                    '[placeholder*="名" i]'
                ]
            elif field_type == "last_name":
                selectors = [
                    '@name=last_name',
                    '@name=lastName',
                    'input[name="last_name"]',
                    'input[name="lastName"]',
                    '#last_name',
                    '#lastName',
                    '[placeholder*="last" i]',
                    '[placeholder*="姓" i]'
                ]
            else:  # full_name
                selectors = [
                    '@name=name',
                    '@name=full_name',
                    '@name=fullName',
                    'input[name="name"]',
                    'input[name="full_name"]',
                    'input[name="fullName"]',
                    '#name',
                    '#full_name',
                    '[placeholder*="name" i]',
                    '[placeholder*="姓名" i]'
                ]
        
        self.logger.info(f"尝试输入{field_type}: {name}")
        
        for selector in selectors:
            if self.input_action.input_name(selector, name, wait_timeout=2):
                self.logger.info(f"成功使用选择器输入{field_type}: {selector}")
                return True
        
        self.logger.error(f"所有{field_type}选择器都失败")
        return False
    
    def input_verification_code_with_selectors(self, code: str, selectors: List[str] = None,
                                              wait_timeout: int = 10) -> bool:
        """
        使用多个选择器尝试输入验证码
        
        Args:
            code (str): 验证码
            selectors (List[str]): 选择器列表，None时使用默认选择器
            wait_timeout (int): 等待超时时间
            
        Returns:
            bool: 是否输入成功
        """
        if selectors is None:
            selectors = [
                '@name=code',
                '@name=verification_code',
                '@name=verificationCode',
                'input[name="code"]',
                'input[name="verification_code"]',
                'input[name="verificationCode"]',
                '#code',
                '#verification_code',
                '.verification-input',
                '@data-index=0',  # 常见的验证码输入框
                '[placeholder*="code" i]',
                '[placeholder*="验证码" i]'
            ]
        
        self.logger.info(f"尝试输入验证码: {code}")
        
        for selector in selectors:
            if self.input_action.input_verification_code(selector, code, wait_timeout=2):
                self.logger.info(f"成功使用选择器输入验证码: {selector}")
                return True
        
        self.logger.error("所有验证码选择器都失败")
        return False
