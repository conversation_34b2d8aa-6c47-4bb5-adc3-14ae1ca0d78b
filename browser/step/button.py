"""
按钮步骤操作模块
提供通用的按钮操作步骤
"""
from typing import List, Optional
from ..action.factory import create_actions
from logger.logger import get_logger


class ButtonStep:
    """按钮步骤操作类"""
    
    def __init__(self, tab):
        """
        初始化按钮步骤操作

        Args:
            tab: 浏览器标签页
        """
        self.tab = tab
        self.logger = get_logger("ButtonStep")
        self.actions = create_actions(tab)
    
    def click_submit_with_selectors(self, selectors: List[str] = None, 
                                   wait_timeout: int = 10) -> bool:
        """
        使用多个选择器尝试点击提交按钮
        
        Args:
            selectors (List[str]): 选择器列表，None时使用默认选择器
            wait_timeout (int): 等待超时时间
            
        Returns:
            bool: 是否点击成功
        """
        if selectors is None:
            selectors = [
                '@type=submit',
                'button[type="submit"]',
                'input[type="submit"]',
                'button:contains("提交")',
                'button:contains("Submit")',
                'button:contains("确认")',
                'button:contains("Confirm")',
                '.submit-btn',
                '.btn-submit',
                '#submit',
                '.submit-button'
            ]
        
        self.logger.info("尝试点击提交按钮")

        for selector in selectors:
            try:
                # 使用新的链式 API
                self.actions.button(selector).click()
                self.logger.info(f"成功使用选择器点击提交按钮: {selector}")
                return True
            except Exception as e:
                self.logger.debug(f"选择器 {selector} 失败: {e}")
                continue

        self.logger.error("所有提交按钮选择器都失败")
        return False
    
    def click_register_with_selectors(self, selectors: List[str] = None,
                                     wait_timeout: int = 10) -> bool:
        """
        使用多个选择器尝试点击注册按钮
        
        Args:
            selectors (List[str]): 选择器列表，None时使用默认选择器
            wait_timeout (int): 等待超时时间
            
        Returns:
            bool: 是否点击成功
        """
        if selectors is None:
            selectors = [
                'button:contains("注册")',
                'button:contains("Register")',
                'button:contains("Sign Up")',
                'button:contains("Create Account")',
                '.register-btn',
                '.btn-register',
                '.signup-btn',
                '.btn-signup',
                '#register',
                '#signup',
                '.register-button',
                '.signup-button'
            ]
        
        self.logger.info("尝试点击注册按钮")

        for selector in selectors:
            try:
                # 使用新的链式 API
                self.actions.button(selector).click()
                self.logger.info(f"成功使用选择器点击注册按钮: {selector}")
                return True
            except Exception as e:
                self.logger.debug(f"选择器 {selector} 失败: {e}")
                continue

        self.logger.error("所有注册按钮选择器都失败")
        return False
    
    def click_login_with_selectors(self, selectors: List[str] = None,
                                  wait_timeout: int = 10) -> bool:
        """
        使用多个选择器尝试点击登录按钮
        
        Args:
            selectors (List[str]): 选择器列表，None时使用默认选择器
            wait_timeout (int): 等待超时时间
            
        Returns:
            bool: 是否点击成功
        """
        if selectors is None:
            selectors = [
                'button:contains("登录")',
                'button:contains("Login")',
                'button:contains("Sign In")',
                '.login-btn',
                '.btn-login',
                '.signin-btn',
                '.btn-signin',
                '#login',
                '#signin',
                '.login-button',
                '.signin-button'
            ]
        
        self.logger.info("尝试点击登录按钮")
        
        for selector in selectors:
            try:
                # 使用新的链式 API
                self.actions.button(selector).click()
                self.logger.info(f"成功使用选择器点击登录按钮: {selector}")
                return True
            except Exception as e:
                self.logger.debug(f"选择器 {selector} 失败: {e}")
                continue

        self.logger.error("所有登录按钮选择器都失败")
        return False
    
    def click_continue_with_selectors(self, selectors: List[str] = None,
                                     wait_timeout: int = 10) -> bool:
        """
        使用多个选择器尝试点击继续按钮
        
        Args:
            selectors (List[str]): 选择器列表，None时使用默认选择器
            wait_timeout (int): 等待超时时间
            
        Returns:
            bool: 是否点击成功
        """
        if selectors is None:
            selectors = [
                'button:contains("继续")',
                'button:contains("Continue")',
                'button:contains("Next")',
                'button:contains("下一步")',
                '.continue-btn',
                '.btn-continue',
                '.next-btn',
                '.btn-next',
                '#continue',
                '#next',
                '.continue-button',
                '.next-button'
            ]
        
        self.logger.info("尝试点击继续按钮")
        
        for selector in selectors:
            try:
                # 使用新的链式 API
                self.actions.button(selector).click()
                self.logger.info(f"成功使用选择器点击继续按钮: {selector}")
                return True
            except Exception as e:
                self.logger.debug(f"选择器 {selector} 失败: {e}")
                continue

        self.logger.error("所有继续按钮选择器都失败")
        return False
    
    def click_checkbox_with_selectors(self, selectors: List[str] = None,
                                     wait_timeout: int = 10) -> bool:
        """
        使用多个选择器尝试点击复选框
        
        Args:
            selectors (List[str]): 选择器列表，None时使用默认选择器
            wait_timeout (int): 等待超时时间
            
        Returns:
            bool: 是否点击成功
        """
        if selectors is None:
            selectors = [
                '#terms-of-service-checkbox',
                '@name=terms-of-service',
                'input[name="terms-of-service"]',
                'input[type="checkbox"][name="terms-of-service"]',
                '.c-checkbox input[type="checkbox"]',
                'input[type="checkbox"]',
                '.checkbox-input',
                '.terms-checkbox'
            ]
        
        self.logger.info("尝试点击复选框")
        
        for selector in selectors:
            try:
                # 使用新的链式 API
                self.actions.button(selector).click()
                self.logger.info(f"成功使用选择器点击复选框: {selector}")
                return True
            except Exception as e:
                self.logger.debug(f"选择器 {selector} 失败: {e}")
                continue

        self.logger.error("所有复选框选择器都失败")
        return False
