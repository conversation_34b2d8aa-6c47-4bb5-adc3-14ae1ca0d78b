"""
表单步骤操作模块
提供通用的表单操作步骤
"""
from typing import Dict, List, Optional
from .input import InputStep
from .button import ButtonStep
from logger.logger import get_logger


class FormStep:
    """表单步骤操作类"""
    
    def __init__(self, tab):
        """
        初始化表单步骤操作
        
        Args:
            tab: 浏览器标签页
        """
        self.tab = tab
        self.logger = get_logger("FormStep")
        self.input_step = InputStep(tab)
        self.button_step = ButtonStep(tab)
    
    def fill_registration_form(self, account_info: Dict[str, str]) -> bool:
        """
        填写注册表单
        
        Args:
            account_info (dict): 账号信息
            
        Returns:
            bool: 是否填写成功
        """
        self.logger.info("开始填写注册表单")
        
        success_count = 0
        total_fields = 0
        
        # 填写邮箱
        if account_info.get('email'):
            total_fields += 1
            if self.input_step.input_email_with_selectors(account_info['email']):
                success_count += 1
            else:
                self.logger.warning("邮箱填写失败")
        
        # 填写密码
        if account_info.get('password'):
            total_fields += 1
            if self.input_step.input_password_with_selectors(account_info['password']):
                success_count += 1
            else:
                self.logger.warning("密码填写失败")
        
        # 填写名字
        if account_info.get('first_name'):
            total_fields += 1
            if self.input_step.input_name_with_selectors(account_info['first_name'], 'first_name'):
                success_count += 1
            else:
                self.logger.warning("名字填写失败")
        
        # 填写姓氏
        if account_info.get('last_name'):
            total_fields += 1
            if self.input_step.input_name_with_selectors(account_info['last_name'], 'last_name'):
                success_count += 1
            else:
                self.logger.warning("姓氏填写失败")
        
        # 填写全名（如果没有分开的名字和姓氏）
        if account_info.get('full_name') and not (account_info.get('first_name') or account_info.get('last_name')):
            total_fields += 1
            if self.input_step.input_name_with_selectors(account_info['full_name'], 'full_name'):
                success_count += 1
            else:
                self.logger.warning("全名填写失败")
        
        self.logger.info(f"表单填写完成: {success_count}/{total_fields} 字段成功")
        
        # 如果至少有一半字段填写成功，认为表单填写成功
        return success_count >= (total_fields / 2) if total_fields > 0 else False
    
    def fill_login_form(self, email: str, password: str) -> bool:
        """
        填写登录表单
        
        Args:
            email (str): 邮箱
            password (str): 密码
            
        Returns:
            bool: 是否填写成功
        """
        self.logger.info("开始填写登录表单")
        
        # 填写邮箱
        if not self.input_step.input_email_with_selectors(email):
            self.logger.error("登录邮箱填写失败")
            return False
        
        # 填写密码
        if not self.input_step.input_password_with_selectors(password):
            self.logger.error("登录密码填写失败")
            return False
        
        self.logger.info("登录表单填写成功")
        return True
    
    def submit_form_with_retry(self, max_retries: int = 3) -> bool:
        """
        提交表单（带重试机制）
        
        Args:
            max_retries (int): 最大重试次数
            
        Returns:
            bool: 是否提交成功
        """
        self.logger.info("开始提交表单")
        
        for attempt in range(max_retries):
            if self.button_step.click_submit_with_selectors():
                self.logger.info(f"表单提交成功 (尝试 {attempt + 1}/{max_retries})")
                return True
            
            if attempt < max_retries - 1:
                self.logger.warning(f"表单提交失败，准备重试 (尝试 {attempt + 1}/{max_retries})")
                import time
                time.sleep(1)
        
        self.logger.error("表单提交失败，已达到最大重试次数")
        return False
    
    def accept_terms_and_conditions(self) -> bool:
        """
        接受条款和条件
        
        Returns:
            bool: 是否成功
        """
        self.logger.info("尝试接受条款和条件")
        
        if self.button_step.click_checkbox_with_selectors():
            self.logger.info("成功接受条款和条件")
            return True
        else:
            self.logger.warning("未找到条款复选框或点击失败")
            return False
    
    def complete_registration_flow(self, account_info: Dict[str, str], 
                                  accept_terms: bool = True) -> bool:
        """
        完成完整的注册流程
        
        Args:
            account_info (dict): 账号信息
            accept_terms (bool): 是否接受条款
            
        Returns:
            bool: 是否成功
        """
        self.logger.info("开始完整注册流程")
        
        # 1. 填写注册表单
        if not self.fill_registration_form(account_info):
            self.logger.error("注册表单填写失败")
            return False
        
        # 2. 接受条款（如果需要）
        if accept_terms:
            self.accept_terms_and_conditions()  # 不强制要求成功
        
        # 3. 提交表单
        if not self.submit_form_with_retry():
            self.logger.error("表单提交失败")
            return False
        
        self.logger.info("完整注册流程完成")
        return True
