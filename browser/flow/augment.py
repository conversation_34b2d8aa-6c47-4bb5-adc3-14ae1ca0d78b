"""
Augment注册流程模块
实现Augment账号注册的完整业务流程
"""
import time
import random
from typing import Dict, Optional
from ..action.input import InputAction
from ..action.button import ButtonAction
from ..action.navigate import NavigateAction
from ..action.turnstile import TurnstileAction
from ..step.input import InputStep
from ..step.button import ButtonStep
from ..step.form import FormStep
import sys
import os
# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))
from logger.logger import get_logger


class AugmentRegistrationFlow:
    """Augment注册流程类"""

    def __init__(self, tab, config=None, email_handler=None):
        """
        初始化Augment注册流程

        Args:
            tab: 浏览器标签页
            config: 配置对象
            email_handler: 邮箱处理器（用于获取验证码）
        """
        self.tab = tab
        self.config = config
        self.email_handler = email_handler

        # 初始化日志器
        self.logger = get_logger("AugmentRegistrationFlow")

        # 如果没有传入邮箱处理器，尝试从配置创建
        if not self.email_handler and self.config:
            try:
                from mail.handler import EmailHandlerFactory
                self.email_handler = EmailHandlerFactory.create_from_config(self.config, 'augment')
                self.logger.info("✅ 从配置自动创建Augment邮箱处理器")
            except Exception as e:
                self.logger.warning(f"⚠️ 从配置创建邮箱处理器失败: {e}")

        # 初始化各个操作模块
        self.input_action = InputAction(tab)
        self.button_action = ButtonAction(tab)
        self.navigate_action = NavigateAction(tab)
        self.turnstile_action = TurnstileAction(tab)

        # 初始化步骤操作模块
        self.input_step = InputStep(tab)
        self.button_step = ButtonStep(tab)
        self.form_step = FormStep(tab)

        # URL配置
        self.urls = {
            'signup': 'https://app.augmentcode.com/',
            'dashboard': 'https://augmentcode.com/dashboard',
            'login': 'https://login.augmentcode.com/',
        }

        # 验证邮箱处理器
        if self.email_handler:
            self.logger.info(f"✅ 已配置邮箱处理器: {type(self.email_handler).__name__}")
            self.logger.info(f"📧 邮箱地址: {self.email_handler.email_address}")
        else:
            self.logger.warning("⚠️ 未配置邮箱处理器，邮箱验证功能将不可用")

    def register_account(self, account_info: Dict[str, str]) -> bool:
        """
        执行完整的账号注册流程
        按照Augment的注册逻辑：
        1. 邮箱填写页面，同时页面包含一个turnstile验证码
        2. 验证通过后点击提交
        3. 检查邮箱验证码，填入验证码
        4. 点击同意复选框
        5. 点击提交按钮

        Args:
            account_info (dict): 账号信息，包含email

        Returns:
            bool: 注册是否成功
        """
        try:
            self.logger.info("=" * 60)
            self.logger.info("🚀 开始Augment账号注册流程")
            self.logger.info(f"📧 邮箱: {account_info.get('email', 'N/A')}")
            self.logger.info("=" * 60)

            # 1. 导航到注册页面
            self.logger.info("📍 步骤 1/5: 导航到注册页面")
            if not self._navigate_to_signup_page():
                self.logger.error("❌ 步骤 1 失败: 无法导航到注册页面")
                return False
            self.logger.info("✅ 步骤 1 完成: 成功导航到注册页面")

            # 2. 填写邮箱并处理Turnstile验证
            self.logger.info("📍 步骤 2/5: 填写邮箱并处理Turnstile验证")
            if not self._fill_email_and_handle_turnstile(account_info):
                self.logger.error("❌ 步骤 2 失败: 邮箱填写或Turnstile验证失败")
                return False
            self.logger.info("✅ 步骤 2 完成: 邮箱填写和Turnstile验证成功")

            # 3. 处理邮箱验证码
            self.logger.info("📍 步骤 3/5: 处理邮箱验证码")
            if not self._handle_email_verification():
                self.logger.error("❌ 步骤 3 失败: 邮箱验证码处理失败")
                return False
            self.logger.info("✅ 步骤 3 完成: 邮箱验证码处理成功")

            # 4. 点击同意复选框
            self.logger.info("📍 步骤 4/5: 点击同意复选框")
            if not self._handle_terms_agreement():
                self.logger.error("❌ 步骤 4 失败: 同意复选框处理失败")
                return False
            self.logger.info("✅ 步骤 4 完成: 同意复选框处理成功")

            # 5. 点击提交按钮
            self.logger.info("📍 步骤 5/5: 点击提交按钮")
            if not self._submit_final_form():
                self.logger.error("❌ 步骤 5 失败: 最终表单提交失败")
                return False
            self.logger.info("✅ 步骤 5 完成: 最终表单提交成功")

            self.logger.info("=" * 60)
            self.logger.info("🎉 Augment账号注册流程全部完成！")
            self.logger.info("=" * 60)
            return True

        except Exception as e:
            self.logger.error("=" * 60)
            self.logger.error(f"💥 注册流程异常: {e}")
            self.logger.error("=" * 60)
            import traceback
            self.logger.error(traceback.format_exc())
            return False

    def _navigate_to_signup_page(self) -> bool:
        """
        导航到注册页面

        Returns:
            bool: 是否导航成功
        """
        try:
            self.logger.info("🌐 正在导航到Augment注册页面...")

            if not self.navigate_action.goto(self.urls['signup']):
                self.logger.error("❌ 导航到注册页面失败")
                return False

            self.logger.info("📄 页面请求已发送，等待加载...")
            # 等待页面加载完成
            time.sleep(15)

            # 验证是否在正确的页面
            current_url = self.navigate_action.get_current_url()
            self.logger.info(f"🔍 当前URL: {current_url}")

            if not current_url or 'augmentcode.com' not in current_url:
                self.logger.error(f"❌ 未正确导航到注册页面，当前URL: {current_url}")
                return False

            # 检查页面是否加载完成（查找邮箱输入框）
            if self.tab.ele("@inputmode=email", timeout=10) or self.tab.ele("input[inputmode='email']", timeout=5):
                self.logger.info("✅ 注册页面加载完成，检测到邮箱输入框")
            else:
                self.logger.warning("⚠️ 未检测到邮箱输入框，页面可能未完全加载")

            return True

        except Exception as e:
            self.logger.error(f"💥 导航到注册页面失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False

    def _fill_email_and_handle_turnstile(self, account_info: Dict[str, str]) -> bool:
        """
        填写邮箱并处理Turnstile验证
        按照Augment的逻辑：邮箱填写页面同时包含Turnstile验证码

        Args:
            account_info (dict): 账号信息

        Returns:
            bool: 是否填写和验证成功
        """
        try:
            self.logger.info("📝 开始填写邮箱...")

            # 填写邮箱
            email = account_info.get('email', '')
            if not email:
                self.logger.error("❌ 邮箱地址为空")
                return False

            self.logger.info(f"📧 正在输入邮箱: {email}")

            # 使用 input_step 填写邮箱
            email_selectors = [
                '@inputmode=email',
                '@name=username',
                'input[inputmode="email"]',
                'input[name="username"]',
                '#email',
                '.email-input'
            ]

            if not self.input_step.input_email_with_selectors(email, email_selectors):
                self.logger.error("❌ 填写邮箱失败，尝试了所有选择器")
                return False

            self.logger.info("✅ 邮箱填写完成")
            time.sleep(random.uniform(1, 2))

            # 处理Turnstile验证
            self.logger.info("🔐 开始处理Turnstile验证...")
            if not self._handle_turnstile_verification():
                self.logger.error("❌ Turnstile验证失败")
                return False

            self.logger.info("✅ Turnstile验证完成")

            # 提交表单
            self.logger.info("📤 正在提交表单...")
            if not self._submit_form():
                self.logger.error("❌ 表单提交失败")
                return False

            self.logger.info("✅ 表单提交成功")
            return True

        except Exception as e:
            self.logger.error(f"💥 填写邮箱和处理Turnstile验证失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False

    def _handle_turnstile_verification(self) -> bool:
        """
        处理Turnstile验证（使用原始逻辑）

        Returns:
            bool: 验证是否成功
        """
        self.logger.info("🔐 处理Turnstile验证...")

        # 使用原始的handle_turnstile_verification方法
        return self.turnstile_action.handle_turnstile_verification("ulp-auth0-v2-captcha")

    def _submit_form(self) -> bool:
        """
        提交表单

        Returns:
            bool: 是否提交成功
        """
        try:
            self.logger.info("📤 正在提交表单...")

            # 使用 button_step 提交表单
            if self.button_step.click_submit_with_selectors(['@type=submit']):
                self.logger.info("✅ 成功提交表单")
                time.sleep(2)  # 等待提交处理
                return True

            # 备用方法：尝试其他提交方式
            submit_methods = [
                lambda: self._submit_with_drissionpage(),
                lambda: self.button_step.click_register_with_selectors(),
                lambda: self._submit_by_enter_key()
            ]

            for method in submit_methods:
                try:
                    if method():
                        self.logger.info("✅ 成功提交表单")
                        time.sleep(2)  # 等待提交处理
                        return True
                except Exception as e:
                    self.logger.debug(f"提交方法失败: {e}")
                    continue

            self.logger.error("❌ 提交表单失败，尝试了所有方法")
            return False

        except Exception as e:
            self.logger.error(f"💥 提交表单异常: {e}")
            return False

    def _submit_with_drissionpage(self) -> bool:
        """
        使用DrissionPage语法提交表单

        Returns:
            bool: 是否提交成功
        """
        try:
            # 尝试使用DrissionPage的actions方式点击提交按钮
            if self.tab.ele("@type=submit", timeout=5):
                self.tab.actions.click("@type=submit")
                return True
            return False
        except Exception:
            return False

    def _submit_by_enter_key(self) -> bool:
        """
        通过回车键提交表单

        Returns:
            bool: 是否提交成功
        """
        try:
            # 在邮箱框中按回车
            email_element = self.tab.ele('@type=email', timeout=2) or self.tab.ele('input[type="email"]', timeout=2)
            if email_element:
                email_element.input('\n')
                return True
        except Exception:
            pass

        return False

    def _handle_email_verification(self) -> bool:
        """
        处理邮箱验证码

        Returns:
            bool: 验证是否成功
        """
        try:
            self.logger.info("📧 开始处理邮箱验证码...")

            # 等待验证码输入页面出现
            self.logger.info("🔍 等待验证码输入页面...")

            # 检查是否有验证码输入框
            verification_selectors = [
                '@name=code',
                '@name=verification_code',
                '@name=verificationCode',
                '@type=text',
                'input[name="code"]',
                'input[name="verification_code"]',
                'input[name="verificationCode"]',
                'input[type="text"]',
                '.verification-input',
                '.code-input'
            ]

            # 检查是否有验证码输入框
            has_verification_input = False
            for selector in verification_selectors:
                try:
                    element = self.tab.ele(selector, timeout=10)
                    if element:
                        has_verification_input = True
                        self.logger.info(f"✅ 找到验证码输入框: {selector}")
                        break
                except Exception:
                    continue

            if not has_verification_input:
                self.logger.warning("⚠️ 未找到验证码输入框，可能不需要邮箱验证")
                return True

            # 检查是否有邮箱处理器
            if not self.email_handler:
                self.logger.error("❌ 未配置邮箱处理器，无法获取验证码")
                self.logger.error("💡 请在初始化AugmentRegistrationFlow时传入email_handler参数")
                return False

            # 获取验证码
            self.logger.info("📬 正在获取邮箱验证码...")
            code = self.email_handler.get_verification_code(max_retries=3, retry_interval=30)

            if not code:
                self.logger.error("❌ 获取验证码失败")
                return False

            self.logger.info(f"✅ 成功获取验证码: {code}")

            # 使用 input_step 输入验证码
            self.logger.info("⌨️ 正在输入验证码...")
            if not self.input_step.input_verification_code_with_selectors(code, verification_selectors):
                self.logger.error("❌ 输入验证码失败")
                return False

            self.logger.info("✅ 验证码输入完成")
            time.sleep(random.uniform(1, 2))

            # 提交验证码（可能需要点击提交按钮或自动提交）
            self._submit_verification_code()

            return True

        except Exception as e:
            self.logger.error(f"💥 邮箱验证失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False

    def _submit_verification_code(self) -> bool:
        """
        提交验证码

        Returns:
            bool: 是否提交成功
        """
        try:
            self.logger.info("📤 正在提交验证码...")

            # 使用 button_step 提交验证码
            if self.button_step.click_submit_with_selectors(['@type=submit']):
                self.logger.info("✅ 验证码提交成功")
                return True

            # 尝试其他提交方式
            submit_methods = [
                lambda: self.button_action.click_button_by_text('Verify'),
                lambda: self.button_action.click_button_by_text('Submit'),
                lambda: self.button_action.click_button_by_text('Confirm'),
                # 有些验证码是自动提交的，等待一下
                lambda: self._wait_for_auto_submit()
            ]

            for method in submit_methods:
                try:
                    if method():
                        self.logger.info("✅ 验证码提交成功")
                        return True
                except Exception as e:
                    self.logger.debug(f"验证码提交方法失败: {e}")
                    continue

            self.logger.warning("⚠️ 未找到明确的提交按钮，可能是自动提交")
            return True

        except Exception as e:
            self.logger.error(f"💥 提交验证码失败: {e}")
            return False

    def _wait_for_auto_submit(self) -> bool:
        """
        等待自动提交（有些验证码输入后会自动提交）

        Returns:
            bool: 是否检测到自动提交
        """
        try:
            self.logger.info("⏳ 等待可能的自动提交...")
            time.sleep(3)

            # 检查页面是否发生变化（URL变化或出现成功提示）
            current_url = self.navigate_action.get_current_url()
            if current_url and ('dashboard' in current_url or 'success' in current_url):
                self.logger.info("✅ 检测到页面跳转，可能是自动提交成功")
                return True

            # 检查是否有成功提示
            success_selectors = [
                '.success',
                '.success-message',
                '.alert-success',
                '[data-testid="success"]'
            ]

            for selector in success_selectors:
                if self.tab.ele(selector, timeout=1):
                    self.logger.info("✅ 检测到成功提示")
                    return True

            return False

        except Exception:
            return False

    def _wait_for_registration_success(self) -> bool:
        """
        等待注册成功

        Returns:
            bool: 注册是否成功
        """
        try:
            self.logger.info("⏳ 等待注册成功确认...")

            # 等待页面跳转或成功提示
            success_indicators = [
                # URL变化到仪表板或成功页面
                lambda: self._check_dashboard_redirect(),
                # 成功消息
                lambda: self._check_success_message(),
                # 用户信息出现
                lambda: self._check_user_info(),
                # 注册页面消失
                lambda: self._check_signup_page_gone()
            ]

            start_time = time.time()
            timeout = 60  # 60秒超时

            while time.time() - start_time < timeout:
                for indicator in success_indicators:
                    if indicator():
                        self.logger.info("🎉 检测到注册成功")
                        return True

                # 检查是否有错误消息
                if self._check_error_message():
                    self.logger.error("❌ 检测到注册错误")
                    return False

                time.sleep(2)

            self.logger.error("❌ 等待注册成功超时")
            return False

        except Exception as e:
            self.logger.error(f"💥 等待注册成功失败: {e}")
            return False

    def _check_dashboard_redirect(self) -> bool:
        """检查是否跳转到仪表板"""
        current_url = self.navigate_action.get_current_url()
        return current_url and ('dashboard' in current_url or 'home' in current_url)

    def _check_success_message(self) -> bool:
        """检查是否有成功消息"""
        success_selectors = [
            '.success-message',
            '.alert-success',
            '[data-testid="success"]',
            '.notification-success',
            '.registration-success',
            '.signup-success'
        ]

        for selector in success_selectors:
            element = self.tab.ele(selector, timeout=1)
            if element:
                return True

        return False

    def _check_user_info(self) -> bool:
        """检查是否出现用户信息"""
        user_info_selectors = [
            '.user-info',
            '.user-profile',
            '.user-menu',
            '[data-testid="user"]',
            '.avatar',
            '.user-name'
        ]

        for selector in user_info_selectors:
            element = self.tab.ele(selector, timeout=1)
            if element:
                return True

        return False

    def _check_signup_page_gone(self) -> bool:
        """检查注册页面是否消失"""
        current_url = self.navigate_action.get_current_url()
        return current_url and 'login' not in current_url and 'signup' not in current_url

    def _check_error_message(self) -> bool:
        """检查是否有错误消息"""
        error_selectors = [
            '.error-message',
            '.alert-error',
            '.alert-danger',
            '[data-testid="error"]',
            '.notification-error',
            '.registration-error',
            '.signup-error'
        ]

        for selector in error_selectors:
            element = self.tab.ele(selector, timeout=1)
            if element and element.text:
                self.logger.error(f"注册错误: {element.text}")
                return True

        return False

    def get_registration_result(self) -> Optional[Dict[str, str]]:
        """
        获取注册结果信息

        Returns:
            dict: 注册结果信息，获取失败返回None
        """
        try:
            # 尝试从页面元素获取注册结果信息
            result_info = {}

            # 获取用户邮箱
            email_selectors = ['.user-email', '.email', '[data-testid="email"]']
            for selector in email_selectors:
                element = self.tab.ele(selector, timeout=2)
                if element and element.text:
                    result_info['email'] = element.text
                    break

            # 获取注册状态
            current_url = self.navigate_action.get_current_url()
            if current_url:
                if 'dashboard' in current_url:
                    result_info['status'] = 'success'
                    result_info['redirect_url'] = current_url
                elif 'verify' in current_url:
                    result_info['status'] = 'pending_verification'
                    result_info['redirect_url'] = current_url
                else:
                    result_info['status'] = 'unknown'
                    result_info['redirect_url'] = current_url

            if result_info:
                self.logger.info(f"获取到注册结果: {result_info}")
                return result_info

            self.logger.warning("未能获取注册结果信息")
            return None

        except Exception as e:
            self.logger.error(f"获取注册结果失败: {e}")
            return None

    def _handle_terms_agreement(self) -> bool:
        """
        处理同意条款复选框

        Returns:
            bool: 是否处理成功
        """
        try:
            self.logger.info("☑️ 开始处理同意条款复选框...")

            # 等待页面加载完成
            time.sleep(2)

            # 使用 button_step 点击条款同意复选框
            if self.button_step.click_checkbox_with_selectors():
                self.logger.info("✅ 成功点击同意条款复选框")
                time.sleep(random.uniform(0.5, 1))
                return True
            else:
                self.logger.error("❌ 点击同意条款复选框失败")
                return False

        except Exception as e:
            self.logger.error(f"💥 处理同意条款复选框失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False

    def _submit_final_form(self) -> bool:
        """
        提交最终表单（点击"Sign up and start coding"按钮）

        Returns:
            bool: 是否提交成功
        """
        try:
            self.logger.info("📤 开始提交最终表单...")

            # 等待按钮变为可用状态
            signup_button_selectors = [
                '#signup-button',
                'button:contains("Sign up and start coding")',
                'button:contains("Sign up")',
                '.sign-link',
                '.gsi-material-button'
            ]

            # 等待按钮可用
            button_enabled = False
            for selector in signup_button_selectors:
                if self.button_action.wait_for_button_enabled(selector, timeout=10):
                    self.logger.info(f"✅ 按钮已可用: {selector}")
                    button_enabled = True
                    break

            if not button_enabled:
                self.logger.warning("⚠️ 未检测到按钮可用状态，尝试直接点击")

            # 使用 button_step 尝试点击提交按钮
            if self.button_step.click_submit_with_selectors(signup_button_selectors):
                self.logger.info("✅ 成功点击提交按钮")
                time.sleep(3)  # 等待提交处理
                return True

            # 备用方法：逐个尝试
            for selector in signup_button_selectors:
                try:
                    if self.button_action.click_button(selector):
                        self.logger.info(f"✅ 成功点击提交按钮: {selector}")
                        time.sleep(3)  # 等待提交处理
                        return True
                except Exception as e:
                    self.logger.debug(f"尝试点击按钮 {selector} 失败: {e}")
                    continue

            # 如果上述方法都失败，尝试使用JavaScript点击
            try:
                signup_button = self.tab.ele('#signup-button', timeout=5)
                if signup_button:
                    # 使用JavaScript点击
                    self.tab.run_js('document.getElementById("signup-button").click()')
                    self.logger.info("✅ 使用JavaScript成功点击提交按钮")
                    time.sleep(3)
                    return True
            except Exception as e:
                self.logger.debug(f"JavaScript点击失败: {e}")

            self.logger.error("❌ 提交最终表单失败，尝试了所有方法")
            return False

        except Exception as e:
            self.logger.error(f"💥 提交最终表单异常: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False
